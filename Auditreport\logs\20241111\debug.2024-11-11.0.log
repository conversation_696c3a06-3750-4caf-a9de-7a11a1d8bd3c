[32m2024-11-11 17:12:12.448[0;39m [[31mmain[0;39m] [31mWARN [0;39m o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer - 

Found multiple occurrences of org.json.JSONObject on the class path:

	jar:file:/E:/maven/mavenRepository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class
	jar:file:/E:/maven/mavenRepository/org/json/json/20140107/json-20140107.jar!/org/json/JSONObject.class

You may wish to exclude one of them to ensure predictable runtime behaviour

[32m2024-11-11 17:12:12.456[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.DynamicDataSourceTest - Starting DynamicDataSourceTest on pumpkin with PID 24996 (started by 35382 in D:\IdeaWorkspace\AppTools\Auditreport)
[32m2024-11-11 17:12:12.457[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.DynamicDataSourceTest - The following profiles are active: dev
[32m2024-11-11 17:12:12.477[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.context.support.GenericWebApplicationContext - Refreshing org.springframework.web.context.support.GenericWebApplicationContext@37091312: startup date [Mon Nov 11 17:12:12 CST 2024]; root of context hierarchy
[32m2024-11-11 17:12:13.447[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
[32m2024-11-11 17:12:13.625[0;39m [[31mmain[0;39m] [31mWARN [0;39m o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'beanNamePlaceholderRegistryPostProcessor' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
[32m2024-11-11 17:12:13.785[0;39m [[34mmain[0;39m] [34mINFO [0;39m c.u.j.EnableEncryptablePropertySourcesPostProcessor - Post-processing PropertySource instances
[32m2024-11-11 17:12:13.797[0;39m [[34mmain[0;39m] [34mINFO [0;39m c.u.j.configuration.StringEncryptorConfiguration - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing String Encryptor based on properties with name 'jasyptStringEncryptor'
[32m2024-11-11 17:12:13.800[0;39m [[34mmain[0;39m] [34mINFO [0;39m c.u.j.EnableEncryptablePropertySourcesPostProcessor - Converting PropertySource Inlined Test Properties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2024-11-11 17:12:13.801[0;39m [[34mmain[0;39m] [34mINFO [0;39m c.u.j.EnableEncryptablePropertySourcesPostProcessor - Converting PropertySource test [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2024-11-11 17:12:13.801[0;39m [[34mmain[0;39m] [34mINFO [0;39m c.u.j.EnableEncryptablePropertySourcesPostProcessor - Converting PropertySource systemProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2024-11-11 17:12:13.801[0;39m [[34mmain[0;39m] [34mINFO [0;39m c.u.j.EnableEncryptablePropertySourcesPostProcessor - Converting PropertySource systemEnvironment [org.springframework.core.env.SystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
[32m2024-11-11 17:12:13.801[0;39m [[34mmain[0;39m] [34mINFO [0;39m c.u.j.EnableEncryptablePropertySourcesPostProcessor - Converting PropertySource random [org.springframework.boot.context.config.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[32m2024-11-11 17:12:13.801[0;39m [[34mmain[0;39m] [34mINFO [0;39m c.u.j.EnableEncryptablePropertySourcesPostProcessor - Converting PropertySource applicationConfig: [classpath:/application-dev.yml] [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2024-11-11 17:12:13.801[0;39m [[34mmain[0;39m] [34mINFO [0;39m c.u.j.EnableEncryptablePropertySourcesPostProcessor - Converting PropertySource applicationConfig: [classpath:/application.yml] [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2024-11-11 17:12:13.812[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
[32m2024-11-11 17:12:13.823[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.suke.czx.config.ShiroConfig$$EnhancerBySpringCGLIB$$80ebce5d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:13.960[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties' of type [org.mybatis.spring.boot.autoconfigure.MybatisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:13.967[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' of type [org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$9d10460f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:13.970[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceConfig' of type [com.suke.czx.datasources.DynamicDataSourceConfig$$EnhancerBySpringCGLIB$$c6c769f8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:14.043[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:14.050[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration' of type [com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:14.207[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'statFilter' of type [com.alibaba.druid.filter.stat.StatFilter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:14.345[0;39m [[34mmain[0;39m] [34mINFO [0;39m c.u.j.configuration.StringEncryptorConfiguration - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
[32m2024-11-11 17:12:14.345[0;39m [[34mmain[0;39m] [34mINFO [0;39m c.u.j.configuration.StringEncryptorConfiguration - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
[32m2024-11-11 17:12:14.345[0;39m [[34mmain[0;39m] [34mINFO [0;39m c.u.j.configuration.StringEncryptorConfiguration - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
[32m2024-11-11 17:12:14.346[0;39m [[34mmain[0;39m] [34mINFO [0;39m c.u.j.configuration.StringEncryptorConfiguration - Encryptor config not found for property jasypt.encryptor.providerName, using default value: SunJCE
[32m2024-11-11 17:12:14.346[0;39m [[34mmain[0;39m] [34mINFO [0;39m c.u.j.configuration.StringEncryptorConfiguration - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
[32m2024-11-11 17:12:14.364[0;39m [[34mmain[0;39m] [34mINFO [0;39m c.u.j.configuration.StringEncryptorConfiguration - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
[32m2024-11-11 17:12:14.922[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'firstDataSource' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:14.997[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'secondDataSource' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:15.002[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.suke.czx.datasources.DynamicDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:15.307[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:15.314[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:15.314[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sysMenuDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:15.315[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sysMenuDao' of type [com.sun.proxy.$Proxy103] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:15.318[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sysUserDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:15.319[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sysUserDao' of type [com.sun.proxy.$Proxy104] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:15.321[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sysUserTokenDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:15.322[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sysUserTokenDao' of type [com.sun.proxy.$Proxy105] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:15.322[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroServiceImpl' of type [com.suke.czx.modules.sys.service.impl.ShiroServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:15.324[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sysRoleDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:15.326[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sysRoleDao' of type [com.sun.proxy.$Proxy106] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:15.327[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'OAuth2Realm' of type [com.suke.czx.modules.sys.oauth2.OAuth2Realm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:15.345[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sessionManager' of type [org.apache.shiro.web.session.mgt.DefaultWebSessionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:15.826[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:15.886[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:15.907[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$6a3bf4df] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[32m2024-11-11 17:12:16.090[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.Application - =======================additional config load successfully: loaded-ai-version01
[32m2024-11-11 17:12:16.650[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService  'taskScheduler'
[32m2024-11-11 17:12:17.148[0;39m [[34mmain[0;39m] [34mINFO [0;39m org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
[32m2024-11-11 17:12:17.157[0;39m [[34mmain[0;39m] [34mINFO [0;39m org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[32m2024-11-11 17:12:17.157[0;39m [[34mmain[0;39m] [34mINFO [0;39m org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.0 created.
[32m2024-11-11 17:12:18.070[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
[32m2024-11-11 17:12:18.073[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.scheduling.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
[32m2024-11-11 17:12:18.075[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.scheduling.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
[32m2024-11-11 17:12:18.075[0;39m [[34mmain[0;39m] [34mINFO [0;39m org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.0) 'RenrenScheduler' with instanceId 'pumpkin1731316337149'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

[32m2024-11-11 17:12:18.075[0;39m [[34mmain[0;39m] [34mINFO [0;39m org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'RenrenScheduler' initialized from an externally provided properties instance.
[32m2024-11-11 17:12:18.075[0;39m [[34mmain[0;39m] [34mINFO [0;39m org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.0
[32m2024-11-11 17:12:18.076[0;39m [[34mmain[0;39m] [34mINFO [0;39m org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@1828ab51
[32m2024-11-11 17:12:19.408[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/aiTools/AITool/generateAIAuditFileFromSql],methods=[POST]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.AI.controller.AIController.generateAIAuditFileFromSql(com.suke.czx.modules.AI.entity.GenerateAIFileConditionVo,javax.servlet.http.HttpServletRequest)
[32m2024-11-11 17:12:19.409[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/aiTools/AITool/download],methods=[GET]}" onto public void com.suke.czx.modules.AI.controller.AIController.downloadAiSourceFile(java.lang.String,javax.servlet.http.HttpServletResponse) throws java.lang.Exception
[32m2024-11-11 17:12:19.409[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/aiTools/AITool/uploadFile],methods=[POST]}" onto public java.lang.String com.suke.czx.modules.AI.controller.AIController.uploadAiFile(org.springframework.web.multipart.MultipartFile[],javax.servlet.http.HttpServletRequest) throws java.lang.Exception
[32m2024-11-11 17:12:19.410[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/aiTools/AITool/listAuditAiFile],methods=[POST]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.AI.controller.AIController.listAuditAiFile(com.suke.czx.modules.AI.entity.AuditAiFileQryCondition)
[32m2024-11-11 17:12:19.410[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/aiTools/AITool/delAuditAiFile],methods=[POST]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.AI.controller.AIController.delAuditAiFile(java.util.List<java.lang.String>)
[32m2024-11-11 17:12:19.410[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/aiTools/AITool/auditAi],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.AI.controller.AIController.AI(java.lang.String,java.lang.String)
[32m2024-11-11 17:12:19.410[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/aiTools/AITool/downloadAuditedFile],methods=[GET]}" onto public void com.suke.czx.modules.AI.controller.AIController.downloadAiFile(java.lang.String,javax.servlet.http.HttpServletResponse)
[32m2024-11-11 17:12:19.411[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/aiTrain/getFileName],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.aitrain.controller.AiTrainController.getFileName()
[32m2024-11-11 17:12:19.411[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/aiTrain/generateTrainDataFile],methods=[POST]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.aitrain.controller.AiTrainController.generateTrainDataFileFromSql(com.suke.czx.modules.aitrain.entity.GenerateFileFromSqlVO,javax.servlet.http.HttpServletResponse,javax.servlet.http.HttpServletRequest)
[32m2024-11-11 17:12:19.411[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/aiTrain/upload],methods=[POST]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.aitrain.controller.AiTrainController.uploadModelTrainFile(org.springframework.web.multipart.MultipartFile,java.lang.String) throws java.lang.Exception
[32m2024-11-11 17:12:19.412[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/aiTrain/parse],methods=[POST]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.aitrain.controller.AiTrainController.parseModelTrainFile(com.suke.czx.modules.aitrain.entity.ParseVO)
[32m2024-11-11 17:12:19.412[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/aiTrain/train],methods=[POST]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.aitrain.controller.AiTrainController.modelTrain(com.suke.czx.modules.aitrain.entity.ModelTrainParam)
[32m2024-11-11 17:12:19.413[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/modelTrain/getModelTrainInfo],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.aitrain.controller.ModelTrainController.getModelTrainInfo(java.lang.String)
[32m2024-11-11 17:12:19.413[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/modelTrain/getModelAndScoreTest],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.aitrain.controller.ModelTrainController.getModelAndScoreTest(java.lang.String)
[32m2024-11-11 17:12:19.413[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/modelTrain/getTrainName],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.aitrain.controller.ModelTrainController.getTrainName()
[32m2024-11-11 17:12:19.413[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/modelTrain/getModelSum],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.aitrain.controller.ModelTrainController.getModelSum()
[32m2024-11-11 17:12:19.413[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/app/login],methods=[POST]}" onto public com.suke.czx.common.utils.AppBaseResult com.suke.czx.modules.app.controller.ApiLoginController.login(com.suke.czx.common.utils.AppBaseResult) throws java.lang.Exception
[32m2024-11-11 17:12:19.414[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/app/register],methods=[POST]}" onto public com.suke.czx.common.utils.AppBaseResult com.suke.czx.modules.app.controller.ApiRegisterController.register(com.suke.czx.common.utils.AppBaseResult) throws java.lang.Exception
[32m2024-11-11 17:12:19.415[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/app/userId],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.app.controller.ApiTestController.userInfo(java.lang.Integer)
[32m2024-11-11 17:12:19.415[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/app/userInfo],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.app.controller.ApiTestController.userInfo(com.suke.czx.modules.user.entity.UserEntity)
[32m2024-11-11 17:12:19.415[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/app/notToken],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.app.controller.ApiTestController.notToken()
[32m2024-11-11 17:12:19.416[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/app/appUpdate/update],methods=[POST]}" onto public com.suke.czx.common.utils.AppBaseResult com.suke.czx.modules.app.controller.appUpdate.AppUpdateController.update(com.suke.czx.common.utils.AppBaseResult) throws java.lang.Exception
[32m2024-11-11 17:12:19.417[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/app/appUpdate/delete],methods=[POST]}" onto public com.suke.czx.common.utils.AppBaseResult com.suke.czx.modules.app.controller.appUpdate.AppUpdateController.delete(com.suke.czx.common.utils.AppBaseResult) throws java.lang.Exception
[32m2024-11-11 17:12:19.417[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/app/appUpdate/list],methods=[POST]}" onto public com.suke.czx.common.utils.AppBaseResult com.suke.czx.modules.app.controller.appUpdate.AppUpdateController.list(com.suke.czx.common.utils.AppBaseResult) throws java.lang.Exception
[32m2024-11-11 17:12:19.417[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/app/appUpdate/save],methods=[POST]}" onto public com.suke.czx.common.utils.AppBaseResult com.suke.czx.modules.app.controller.appUpdate.AppUpdateController.save(com.suke.czx.common.utils.AppBaseResult) throws java.lang.Exception
[32m2024-11-11 17:12:19.417[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/app/appUpdate/info],methods=[POST]}" onto public com.suke.czx.common.utils.AppBaseResult com.suke.czx.modules.app.controller.appUpdate.AppUpdateController.info(com.suke.czx.common.utils.AppBaseResult) throws java.lang.Exception
[32m2024-11-11 17:12:19.419[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/report/result/delete],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.audit.controller.AuditComparisonController.deleteAuditComparison(java.lang.Long) throws java.lang.Exception
[32m2024-11-11 17:12:19.419[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/report/result/getComparisonList],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.audit.controller.AuditComparisonController.getComparisonList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.Integer,java.lang.String)
[32m2024-11-11 17:12:19.420[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/report/result/deleteBatch],methods=[DELETE]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.audit.controller.AuditComparisonController.delComparisonBatch(java.lang.Long[]) throws java.lang.Exception
[32m2024-11-11 17:12:19.420[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/report/result/downloadFile],methods=[GET]}" onto public void com.suke.czx.modules.audit.controller.AuditComparisonController.downloadComparisonFile(java.lang.String,javax.servlet.http.HttpServletResponse)
[32m2024-11-11 17:12:19.420[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/report/result/preview],methods=[GET]}" onto public void com.suke.czx.modules.audit.controller.AuditComparisonController.previewExcel(java.lang.String,javax.servlet.http.HttpServletResponse) throws java.io.IOException
[32m2024-11-11 17:12:19.420[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/report/result/batchExport],methods=[POST]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.audit.controller.AuditComparisonController.batchExport(java.lang.Long[])
[32m2024-11-11 17:12:19.420[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/report/result/comparison],methods=[POST]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.audit.controller.AuditComparisonController.comparisonReport(java.lang.String,java.lang.String,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
[32m2024-11-11 17:12:19.422[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/report/delete],methods=[DELETE]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.audit.controller.AuditReportController.deleteReport(java.lang.Long[]) throws java.lang.Exception
[32m2024-11-11 17:12:19.422[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/report/upload],methods=[POST]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.audit.controller.AuditReportController.uploadReport(org.springframework.web.multipart.MultipartFile[],javax.servlet.http.HttpServletRequest)
[32m2024-11-11 17:12:19.422[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/report/update],methods=[PUT]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.audit.controller.AuditReportController.uploadReport(java.lang.Long,java.lang.String)
[32m2024-11-11 17:12:19.422[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/report/list],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.audit.controller.AuditReportController.getAllReports(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String)
[32m2024-11-11 17:12:19.422[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/report/download],methods=[GET]}" onto public void com.suke.czx.modules.audit.controller.AuditReportController.downloadReport(java.lang.String,javax.servlet.http.HttpServletResponse)
[32m2024-11-11 17:12:19.423[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/schedule/run]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.job.controller.ScheduleJobController.run(java.lang.Long[])
[32m2024-11-11 17:12:19.423[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/schedule/update]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.job.controller.ScheduleJobController.update(com.suke.czx.modules.job.entity.ScheduleJobEntity)
[32m2024-11-11 17:12:19.423[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/schedule/delete]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.job.controller.ScheduleJobController.delete(java.lang.Long[])
[32m2024-11-11 17:12:19.423[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/schedule/resume]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.job.controller.ScheduleJobController.resume(java.lang.Long[])
[32m2024-11-11 17:12:19.423[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/schedule/list]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.job.controller.ScheduleJobController.list(java.util.Map<java.lang.String, java.lang.Object>)
[32m2024-11-11 17:12:19.423[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/schedule/save]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.job.controller.ScheduleJobController.save(com.suke.czx.modules.job.entity.ScheduleJobEntity)
[32m2024-11-11 17:12:19.423[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/schedule/info/{jobId}]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.job.controller.ScheduleJobController.info(java.lang.Long)
[32m2024-11-11 17:12:19.423[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/schedule/pause]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.job.controller.ScheduleJobController.pause(java.lang.Long[])
[32m2024-11-11 17:12:19.424[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/scheduleLog/list]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.job.controller.ScheduleJobLogController.list(java.util.Map<java.lang.String, java.lang.Object>)
[32m2024-11-11 17:12:19.424[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/scheduleLog/info/{logId}]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.job.controller.ScheduleJobLogController.info(java.lang.Long)
[32m2024-11-11 17:12:19.425[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/log/list],methods=[POST]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.log.controller.OperationController.getAllLog(java.lang.Integer,java.lang.Integer,com.suke.czx.modules.log.dto.OperationLogDTO)
[32m2024-11-11 17:12:19.425[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/system/log/list]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.log.controller.SystemLogController.getSystemLogs(java.lang.Integer,java.lang.Integer)
[32m2024-11-11 17:12:19.426[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/oss/delete]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.oss.controller.SysOssController.delete(java.lang.Long[])
[32m2024-11-11 17:12:19.426[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/oss/list]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.oss.controller.SysOssController.list(java.util.Map<java.lang.String, java.lang.Object>)
[32m2024-11-11 17:12:19.426[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/oss/config]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.oss.controller.SysOssController.config()
[32m2024-11-11 17:12:19.427[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/oss/saveConfig]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.oss.controller.SysOssController.saveConfig(com.suke.czx.modules.oss.cloud.CloudStorageConfig)
[32m2024-11-11 17:12:19.427[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/oss/upload]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.oss.controller.SysOssController.upload(org.springframework.web.multipart.MultipartFile) throws java.lang.Exception
[32m2024-11-11 17:12:19.429[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/rule/config/list],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.rule.controller.AuditRuleConfigController.getRuleConfigList(java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)
[32m2024-11-11 17:12:19.429[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/rule/config/getRuleInfo/{id}],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.rule.controller.AuditRuleConfigController.getRuleConfigInfoById(java.lang.Long)
[32m2024-11-11 17:12:19.429[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/rule/config/update],methods=[PUT]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.rule.controller.AuditRuleConfigController.updateRuleConfig(com.suke.czx.modules.rule.entity.AuditRuleConfig)
[32m2024-11-11 17:12:19.429[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/rule/config/queryFaRule/{id}],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.rule.controller.AuditRuleConfigController.queryUserById(java.lang.String) throws java.io.IOException
[32m2024-11-11 17:12:19.429[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/rule/config/addRuleConfig],methods=[POST]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.rule.controller.AuditRuleConfigController.addRuleConfig(com.suke.czx.modules.rule.entity.AuditRuleConfig)
[32m2024-11-11 17:12:19.429[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/rule/config/delRuleById/{id}],methods=[DELETE]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.rule.controller.AuditRuleConfigController.delRuleConfig(java.lang.Long)
[32m2024-11-11 17:12:19.429[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/rule/config/downloadFaRule/{id}],methods=[GET]}" onto public org.springframework.http.ResponseEntity<java.lang.Object> com.suke.czx.modules.rule.controller.AuditRuleConfigController.exportUserById(java.lang.String) throws java.io.IOException
[32m2024-11-11 17:12:19.429[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/rule/config/delRuleByIds],methods=[DELETE]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.rule.controller.AuditRuleConfigController.delRuleByIds(com.suke.czx.modules.rule.entity.RuleIds)
[32m2024-11-11 17:12:19.430[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/rule/download],methods=[GET]}" onto public void com.suke.czx.modules.rule.controller.AuditRuleController.downloadComparisonFile(java.lang.String,javax.servlet.http.HttpServletResponse)
[32m2024-11-11 17:12:19.430[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/rule/delete/{id}],methods=[DELETE]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.rule.controller.AuditRuleController.deleteRule(java.lang.String)
[32m2024-11-11 17:12:19.431[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/rule/upload],methods=[POST]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.rule.controller.AuditRuleController.uploadRule(org.springframework.web.multipart.MultipartFile)
[32m2024-11-11 17:12:19.431[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/rule/list],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.rule.controller.AuditRuleController.getAllRule(java.lang.String)
[32m2024-11-11 17:12:19.431[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/rule/update/{id}],methods=[PUT]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.rule.controller.AuditRuleController.updateRule(java.lang.String,java.lang.String)
[32m2024-11-11 17:12:19.432[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/config/update]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysConfigController.update(com.suke.czx.modules.sys.entity.SysConfigEntity)
[32m2024-11-11 17:12:19.432[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/config/delete]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysConfigController.delete(java.lang.Long[])
[32m2024-11-11 17:12:19.433[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/config/list]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysConfigController.list(java.util.Map<java.lang.String, java.lang.Object>)
[32m2024-11-11 17:12:19.433[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/config/save]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysConfigController.save(com.suke.czx.modules.sys.entity.SysConfigEntity)
[32m2024-11-11 17:12:19.433[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/config/info/{id}]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysConfigController.info(java.lang.Long)
[32m2024-11-11 17:12:19.433[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/log/list]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysLogController.list(java.util.Map<java.lang.String, java.lang.Object>)
[32m2024-11-11 17:12:19.433[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/logout],methods=[POST]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysLoginController.logout()
[32m2024-11-11 17:12:19.433[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/sys/login],methods=[POST]}" onto public java.util.Map<java.lang.String, java.lang.Object> com.suke.czx.modules.sys.controller.SysLoginController.login(com.suke.czx.modules.sys.dto.LoginDto) throws java.io.IOException
[32m2024-11-11 17:12:19.433[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/captcha.jpg]}" onto public void com.suke.czx.modules.sys.controller.SysLoginController.captcha(javax.servlet.http.HttpServletResponse) throws javax.servlet.ServletException,java.io.IOException
[32m2024-11-11 17:12:19.435[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/menu/updateMenu],methods=[PUT]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysMenuController.update(com.suke.czx.modules.sys.entity.SysMenuEntity)
[32m2024-11-11 17:12:19.435[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/menu/delMenu],methods=[DELETE]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysMenuController.delete(long)
[32m2024-11-11 17:12:19.435[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/menu/list]}" onto public java.util.List<com.suke.czx.modules.sys.entity.SysMenuEntity> com.suke.czx.modules.sys.controller.SysMenuController.list()
[32m2024-11-11 17:12:19.435[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/menu/addMenu],methods=[POST]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysMenuController.save(com.suke.czx.modules.sys.entity.SysMenuEntity)
[32m2024-11-11 17:12:19.435[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/menu/info/{menuId}]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysMenuController.info(java.lang.Long)
[32m2024-11-11 17:12:19.435[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/menu/select]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysMenuController.select()
[32m2024-11-11 17:12:19.435[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/menu/nav]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysMenuController.nav()
[32m2024-11-11 17:12:19.435[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/menu/perMenu],methods=[GET]}" onto public java.util.List<com.suke.czx.modules.sys.entity.SysMenuEntity> com.suke.czx.modules.sys.controller.SysMenuController.getUserMenuList()
[32m2024-11-11 17:12:19.436[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/role/update],methods=[PUT]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysRoleController.update(com.suke.czx.modules.sys.entity.SysRoleEntity)
[32m2024-11-11 17:12:19.437[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/role/delete],methods=[DELETE]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysRoleController.delete(java.lang.Long)
[32m2024-11-11 17:12:19.437[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/role/list],methods=[POST]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysRoleController.list(java.util.Map<java.lang.String, java.lang.Object>)
[32m2024-11-11 17:12:19.437[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/role/save],methods=[POST]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysRoleController.save(com.suke.czx.modules.sys.entity.SysRoleEntity)
[32m2024-11-11 17:12:19.437[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/role/info/{id}],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysRoleController.info(java.lang.Long)
[32m2024-11-11 17:12:19.437[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/role/select],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysRoleController.select()
[32m2024-11-11 17:12:19.437[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/role/queryMenuIds],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysRoleController.getMenusByRoleId(java.lang.Long)
[32m2024-11-11 17:12:19.437[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/role/assignMenu/{roleId}],methods=[POST]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysRoleController.assignMenu(java.lang.Long,java.lang.Long[])
[32m2024-11-11 17:12:19.438[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/user/update]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysUserController.update(com.suke.czx.modules.sys.entity.SysUserEntity)
[32m2024-11-11 17:12:19.438[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/user/delUserBatch]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysUserController.delete(java.lang.String[])
[32m2024-11-11 17:12:19.438[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/user/list],methods=[POST]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysUserController.list(com.suke.czx.modules.sys.entity.UserListQryVo)
[32m2024-11-11 17:12:19.438[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/user/save]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysUserController.save(com.suke.czx.modules.sys.entity.SysUserAddVo)
[32m2024-11-11 17:12:19.438[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/user/info/{userId}]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysUserController.info(java.lang.String)
[32m2024-11-11 17:12:19.438[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/user/info]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysUserController.info()
[32m2024-11-11 17:12:19.438[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/user/password]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysUserController.password(java.lang.String,java.lang.String)
[32m2024-11-11 17:12:19.438[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/user/userId],methods=[GET]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysUserController.userId()
[32m2024-11-11 17:12:19.438[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/user/updateUserRole/{userId}],methods=[PUT]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.sys.controller.SysUserController.updateUserRole(java.lang.String,java.lang.Long[])
[32m2024-11-11 17:12:19.439[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/user/update]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.user.controller.UserController.update(com.suke.czx.modules.user.entity.UserEntity)
[32m2024-11-11 17:12:19.439[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/user/delete]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.user.controller.UserController.delete(java.lang.Long[])
[32m2024-11-11 17:12:19.439[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/user/list]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.user.controller.UserController.list(java.util.Map<java.lang.String, java.lang.Object>)
[32m2024-11-11 17:12:19.439[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/user/save]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.user.controller.UserController.save(com.suke.czx.modules.user.entity.UserEntity)
[32m2024-11-11 17:12:19.439[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/user/info/{userId}]}" onto public com.suke.czx.common.utils.R com.suke.czx.modules.user.controller.UserController.info(java.lang.Long)
[32m2024-11-11 17:12:19.444[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/queryAuditLogWithPageInfo],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.queryAuditLogWithPageInfo(com.suke.czx.newland.vo.audit.AuditLogQryVo,int,int)
[32m2024-11-11 17:12:19.445[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/delAuditDictByDictId],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.delAuditDictByDictId(java.lang.Integer)
[32m2024-11-11 17:12:19.445[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/queryAuditLogWithLogIds],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.queryAuditLogWithLogIds(com.suke.czx.newland.vo.LogIdsVo)
[32m2024-11-11 17:12:19.445[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/downloadAuditLogs],methods=[POST]}" onto public org.springframework.http.ResponseEntity<java.lang.Object> com.suke.czx.newland.controller.AuditController.downloadAuditLogs(com.suke.czx.newland.vo.AuditLogDownloadVo) throws java.io.UnsupportedEncodingException
[32m2024-11-11 17:12:19.445[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/editAuditLogAnalysisRes],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.editAuditLogAnalysisRes(com.suke.czx.newland.vo.EditAuditLogVo)
[32m2024-11-11 17:12:19.446[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/queryAuditConfigWithRuleIds],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.queryAuditConfigWithRuleIds(com.suke.czx.newland.vo.RuleIdsVo)
[32m2024-11-11 17:12:19.446[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/downloadAuditRules],methods=[POST]}" onto public org.springframework.http.ResponseEntity<java.lang.Object> com.suke.czx.newland.controller.AuditController.downloadAuditRules(com.suke.czx.newland.vo.RuleIdsVo) throws java.io.UnsupportedEncodingException
[32m2024-11-11 17:12:19.446[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/queryExecStatus],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.queryExecStatus()
[32m2024-11-11 17:12:19.446[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/saveAnalysisRes],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.saveAnalysisRes(com.suke.czx.newland.vo.audit.AuditLogVo)
[32m2024-11-11 17:12:19.446[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/queryDictType],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.queryDictType()
[32m2024-11-11 17:12:19.446[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/addAuditDict],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.addAuditDict(com.suke.czx.newland.vo.audit.AuditDictVo)
[32m2024-11-11 17:12:19.446[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/addAuditType],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.addAuditType(com.suke.czx.newland.vo.audit.AuditDictTypeVo)
[32m2024-11-11 17:12:19.447[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/queryAuditCode],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.queryAuditCode()
[32m2024-11-11 17:12:19.447[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/queryAuditSql],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.queryAuditSql(com.suke.czx.newland.vo.audit.AuditConfigVo)
[32m2024-11-11 17:12:19.447[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/editAuditConfig],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.editAuditConfig(com.suke.czx.newland.vo.audit.AuditConfigVo)
[32m2024-11-11 17:12:19.447[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/editAuditDict],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.editAuditDict(com.suke.czx.newland.vo.audit.AuditDictVo)
[32m2024-11-11 17:12:19.447[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/execAuditProcess],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.execAuditProcess(com.suke.czx.newland.vo.audit.AuditExecInfoVo)
[32m2024-11-11 17:12:19.447[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/queryJobStatus],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.queryJobStatus()
[32m2024-11-11 17:12:19.447[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/saveExecRemark],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.saveExecRemark(com.suke.czx.newland.vo.audit.AuditLogVo)
[32m2024-11-11 17:12:19.448[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/queryHisConfig],methods=[GET]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.queryHisConfig(long,java.lang.String,java.lang.String)
[32m2024-11-11 17:12:19.448[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/queryAuditConfig],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.queryAuditConfig(com.suke.czx.newland.vo.audit.AuditQueryVo)
[32m2024-11-11 17:12:19.448[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/queryAuditCenter],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.queryAuditCenter()
[32m2024-11-11 17:12:19.448[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/addAuditConfig],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.addAuditConfig(com.suke.czx.newland.vo.audit.AuditConfigVo)
[32m2024-11-11 17:12:19.448[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/audit/delAuditCenter],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditController.delAuditCenter(java.lang.String)
[32m2024-11-11 17:12:19.450[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/auditFileForDatasource/listAuditFileWithPageInfo],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditFileForDatasourceController.listAuditFileWithPageInfo(com.suke.czx.newland.dto.AuditUploadQryConditionDto)
[32m2024-11-11 17:12:19.450[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/auditFileForDatasource/delBatchAuditFileWithIds],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditFileForDatasourceController.delAuditFileWithId(java.util.List<java.lang.String>)
[32m2024-11-11 17:12:19.450[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/auditFileForDatasource/delAuditFileWithId/{auditFileId}],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditFileForDatasourceController.delAuditFileWithId(java.lang.String)
[32m2024-11-11 17:12:19.450[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/auditFileForDatasource/editAuditFileDatasourceRule],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditFileForDatasourceController.editAuditFileDatasourceRule(com.suke.czx.newland.dto.AuditFileDatasourceRuleDto)
[32m2024-11-11 17:12:19.450[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/auditFileForDatasource/doAuditFileDatasource/{ruleId}/{fileId}],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditFileForDatasourceController.doAuditFileDatasource(java.lang.String,java.lang.String)
[32m2024-11-11 17:12:19.450[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/auditFileForDatasource/addAuditFileDatasourceRule],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditFileForDatasourceController.addAuditFileDatasourceRule(com.suke.czx.newland.dto.AuditFileDatasourceRuleDto)
[32m2024-11-11 17:12:19.450[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/auditFileForDatasource/delAuditFileDatasourceLogByIds],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditFileForDatasourceController.delAuditFileDatasourceLogByIds(java.util.List<java.lang.String>)
[32m2024-11-11 17:12:19.450[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/auditFileForDatasource/downloadAuditFileDatasourceRes],methods=[POST]}" onto public void com.suke.czx.newland.controller.AuditFileForDatasourceController.downloadAuditFileDatasourceRes(java.lang.String,javax.servlet.http.HttpServletResponse)
[32m2024-11-11 17:12:19.451[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/auditFileForDatasource/delAuditFileDatasourceRule],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditFileForDatasourceController.delAuditFileDatasourceRule(com.suke.czx.newland.dto.AuditFileDatasourceRuleDelDto)
[32m2024-11-11 17:12:19.451[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/auditFileForDatasource/qryAuditFileDatasourceLogWithPageInfo],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditFileForDatasourceController.qryAuditFileDatasourceLogWithPageInfo(com.suke.czx.newland.dto.AuditFileDatasourceLogQryDto)
[32m2024-11-11 17:12:19.451[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/auditFileForDatasource/qryAuditFileDatasourceRuleWithPageInfo],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditFileForDatasourceController.qryAuditFileDatasourceRuleWithPageInfo(com.suke.czx.newland.dto.AuditFileDatasourceRuleQryDto)
[32m2024-11-11 17:12:19.451[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/auditFileForDatasource/uploadAuditFile],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.AuditFileForDatasourceController.uploadAuditFile(java.lang.String,java.lang.String,java.lang.Long,java.lang.Integer,java.lang.Integer,org.springframework.web.multipart.MultipartFile)
[32m2024-11-11 17:12:19.451[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/cron/cronPreview],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.CronController.auditImport(com.suke.czx.newland.vo.CronPreviewVo)
[32m2024-11-11 17:12:19.451[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/datasource/qryAllDatasourceActivated],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.DatasourceController.qryAllDatasourceActivated()
[32m2024-11-11 17:12:19.452[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/datasource/deleteDatasourceByDatasourceId],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.DatasourceController.deleteDatasourceByDatasourceId(java.lang.String)
[32m2024-11-11 17:12:19.452[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/datasource/updateDatasourceByDatasourceId],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.DatasourceController.updateDatasourceByDatasourceId(com.suke.czx.newland.vo.DatasourceInfoUpdateVo)
[32m2024-11-11 17:12:19.452[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/datasource/qryDatasourceWithPageInfo],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.DatasourceController.qryDatasourceWithPageInfo(com.suke.czx.newland.vo.DatasourceInfoQryVo,int,int)
[32m2024-11-11 17:12:19.452[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/datasource/testDatasourceWithInfo],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.DatasourceController.testDatasourceWithInfo(com.suke.czx.newland.vo.DatasourceTestConnectVo)
[32m2024-11-11 17:12:19.452[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/datasource/addDatasource],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.DatasourceController.addDatasource(com.suke.czx.newland.vo.DatasourceInfoAddVo)
[32m2024-11-11 17:12:19.452[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/datasource/testDatasourceWithDatasourceId],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.DatasourceController.testDatasource(java.lang.String)
[32m2024-11-11 17:12:19.453[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/excel/resultImport],methods=[POST]}" onto public com.suke.czx.newland.common.RespInfo com.suke.czx.newland.controller.ExcelController.resultImport(org.springframework.web.multipart.MultipartFile)
[32m2024-11-11 17:12:19.453[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/excel/auditImport],methods=[POST]}" onto public com.suke.czx.newland.common.RespInfo com.suke.czx.newland.controller.ExcelController.auditImport(org.springframework.web.multipart.MultipartFile)
[32m2024-11-11 17:12:19.454[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/homePage/get7DaysAuditItems],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.HomePageController.get7DaysAuditItems(java.lang.String)
[32m2024-11-11 17:12:19.454[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/homePage/todayAuditResult],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.HomePageController.todayAuditResult(java.lang.String)
[32m2024-11-11 17:12:19.454[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/homePage/homepageMetrics],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.HomePageController.homepageMetrics(java.lang.String)
[32m2024-11-11 17:12:19.454[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/homePage/checkLogAnalysis],methods=[POST]}" onto public com.suke.czx.newland.common.Result com.suke.czx.newland.controller.HomePageController.checkLogAnalysis(com.suke.czx.newland.vo.echartstable.CheckLogAnalysisVo,java.lang.String)
[32m2024-11-11 17:12:19.455[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/v2/api-docs],methods=[GET],produces=[application/json || application/hal+json]}" onto public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)
[32m2024-11-11 17:12:19.456[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources/configuration/security]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.SecurityConfiguration> springfox.documentation.swagger.web.ApiResourceController.securityConfiguration()
[32m2024-11-11 17:12:19.456[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources/configuration/ui]}" onto org.springframework.http.ResponseEntity<springfox.documentation.swagger.web.UiConfiguration> springfox.documentation.swagger.web.ApiResourceController.uiConfiguration()
[32m2024-11-11 17:12:19.456[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/swagger-resources]}" onto org.springframework.http.ResponseEntity<java.util.List<springfox.documentation.swagger.web.SwaggerResource>> springfox.documentation.swagger.web.ApiResourceController.swaggerResources()
[32m2024-11-11 17:12:19.458[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
[32m2024-11-11 17:12:19.458[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
[32m2024-11-11 17:12:19.966[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.config.MultipartConfig - 文件上传临时路径:C:\Users\<USER>\tmp
[32m2024-11-11 17:12:19.974[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService  'taskExecutor'
[32m2024-11-11 17:12:20.704[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.RequestMappingHandlerAdapter - Looking for @ControllerAdvice: org.springframework.web.context.support.GenericWebApplicationContext@37091312: startup date [Mon Nov 11 17:12:12 CST 2024]; root of context hierarchy
[32m2024-11-11 17:12:20.827[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.web.servlet.handler.SimpleUrlHandlerMapping - Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
[32m2024-11-11 17:12:20.827[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.web.servlet.handler.SimpleUrlHandlerMapping - Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
[32m2024-11-11 17:12:20.855[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Detected @ExceptionHandler methods in RRExceptionHandler
[32m2024-11-11 17:12:20.937[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.web.servlet.handler.SimpleUrlHandlerMapping - Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
[32m2024-11-11 17:12:20.990[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.b.a.w.WebMvcAutoConfiguration$WelcomePageHandlerMapping - Adding welcome page: class path resource [views/index.html]
[32m2024-11-11 17:12:21.767[0;39m [[34mmain[0;39m] [34mINFO [0;39m o.s.context.support.DefaultLifecycleProcessor - Starting beans in phase 2147483647
[32m2024-11-11 17:12:21.768[0;39m [[34mmain[0;39m] [34mINFO [0;39m s.d.s.web.plugins.DocumentationPluginsBootstrapper - Context refreshed
[32m2024-11-11 17:12:21.783[0;39m [[34mmain[0;39m] [34mINFO [0;39m s.d.s.web.plugins.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
[32m2024-11-11 17:12:21.792[0;39m [[34mmain[0;39m] [34mINFO [0;39m s.d.spring.web.scanners.ApiListingReferenceScanner - Scanning for api listing references
[32m2024-11-11 17:12:21.899[0;39m [[34mmain[0;39m] [34mINFO [0;39m s.d.s.w.r.operation.CachingOperationNameGenerator - Generating unique operation named: userInfoUsingGET_1
[32m2024-11-11 17:12:22.198[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.schedule.impl.CronTaskInitRunner - 初始化库中配置定时任务信息
[32m2024-11-11 17:12:22.224[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.DynamicDataSourceTest - Started DynamicDataSourceTest in 10.166 seconds (JVM running for 10.783)
[32m2024-11-11 17:12:22.266[0;39m [[34mThread-7[0;39m] [34mINFO [0;39m o.s.w.context.support.GenericWebApplicationContext - Closing org.springframework.web.context.support.GenericWebApplicationContext@37091312: startup date [Mon Nov 11 17:12:12 CST 2024]; root of context hierarchy
[32m2024-11-11 17:12:22.268[0;39m [[34mThread-7[0;39m] [34mINFO [0;39m o.s.context.support.DefaultLifecycleProcessor - Stopping beans in phase 2147483647
[32m2024-11-11 17:12:22.275[0;39m [[34mThread-7[0;39m] [34mINFO [0;39m o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
[32m2024-11-11 17:12:22.279[0;39m [[34mThread-7[0;39m] [34mINFO [0;39m o.s.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
[32m2024-11-11 17:12:22.279[0;39m [[34mThread-7[0;39m] [34mINFO [0;39m org.quartz.core.QuartzScheduler - Scheduler RenrenScheduler_$_pumpkin1731316337149 shutting down.
[32m2024-11-11 17:12:22.279[0;39m [[34mThread-7[0;39m] [34mINFO [0;39m org.quartz.core.QuartzScheduler - Scheduler RenrenScheduler_$_pumpkin1731316337149 paused.
[32m2024-11-11 17:12:22.280[0;39m [[34mThread-7[0;39m] [34mINFO [0;39m org.quartz.core.QuartzScheduler - Scheduler RenrenScheduler_$_pumpkin1731316337149 shutdown complete.
[32m2024-11-11 17:12:22.281[0;39m [[34mThread-7[0;39m] [34mINFO [0;39m o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
[32m2024-11-11 17:12:22.302[0;39m [[34mThread-7[0;39m] [34mINFO [0;39m com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
