[32m2024-11-11 17:12:12.456[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.DynamicDataSourceTest - Starting DynamicDataSourceTest on pumpkin with PID 24996 (started by 35382 in D:\IdeaWorkspace\AppTools\Auditreport)
[32m2024-11-11 17:12:12.457[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.DynamicDataSourceTest - The following profiles are active: dev
[32m2024-11-11 17:12:16.090[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.Application - =======================additional config load successfully: loaded-ai-version01
[32m2024-11-11 17:12:19.966[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.config.MultipartConfig - 文件上传临时路径:C:\Users\<USER>\tmp
[32m2024-11-11 17:12:22.198[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.schedule.impl.CronTaskInitRunner - 初始化库中配置定时任务信息
[32m2024-11-11 17:12:22.224[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.DynamicDataSourceTest - Started DynamicDataSourceTest in 10.166 seconds (JVM running for 10.783)
