2024-11-11 17:12:21,931 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.qryAllAuditDictByActived [159] - ==>  Preparing: select DICT_ID,DICT_TYPE_ID,DICT_VALUE,DICT_NAME,DICT_DESC,STATUS,CREATE_DATE,CREATE_PERSON,MODIFY_DATE,MODIFY_PERSON,CRON_EXPRESSION from PUBLIC_RPT_DICT where STATUS='1' order by DICT_ID desc 
2024-11-11 17:12:21,942 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.qryAllAuditDictByActived [159] - ==> Parameters: 
2024-11-11 17:12:22,044 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.qryAllAuditDictByActived [159] - <==      Total: 12
2024-11-11 17:12:22,054 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.queryRptCheckCfgByRuleIdWithActived [159] - ==>  Preparing: select RULE_ID,RULE_NAME,STATUS,CREATE_PERSON,CREATE_DATE,MODIFY_PERSON,MODIFY_DATE,CHK_TYPE_ID,CENTER_ID,MODLE_ID,RULE_SQL,LIMIT_VALUE,RULE_DESC,ENV_TYPE from RPT_CHECK_CFG where MODLE_ID = ? and STATUS = '1' 
2024-11-11 17:12:22,057 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.queryRptCheckCfgByRuleIdWithActived [159] - ==> Parameters: 3008(Integer)
2024-11-11 17:12:22,192 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.queryRptCheckCfgByRuleIdWithActived [159] - <==      Total: 3
