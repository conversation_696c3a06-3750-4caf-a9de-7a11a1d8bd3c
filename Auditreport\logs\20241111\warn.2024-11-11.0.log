[32m2024-11-11 17:12:12.448[0;39m [[31mmain[0;39m] [31mWARN [0;39m o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer - 

Found multiple occurrences of org.json.JSONObject on the class path:

	jar:file:/E:/maven/mavenRepository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class
	jar:file:/E:/maven/mavenRepository/org/json/json/20140107/json-20140107.jar!/org/json/JSONObject.class

You may wish to exclude one of them to ensure predictable runtime behaviour

[32m2024-11-11 17:12:13.625[0;39m [[31mmain[0;39m] [31mWARN [0;39m o.s.c.annotation.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'beanNamePlaceholderRegistryPostProcessor' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
