[32m2024-12-13 14:49:14.637[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.DynamicDataSourceTest - Starting DynamicDataSourceTest on pumpkin with PID 21036 (started by 35382 in D:\IdeaWorkspace\AppTools\Auditreport)
[32m2024-12-13 14:49:14.638[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.DynamicDataSourceTest - The following profiles are active: dev
[32m2024-12-13 14:49:18.692[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.Application - =======================additional config load successfully: loaded-ai-version01
[32m2024-12-13 14:49:24.108[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.config.MultipartConfig - 文件上传临时路径:C:\Users\<USER>\tmp
[32m2024-12-13 14:49:27.053[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.schedule.impl.CronTaskInitRunner - 初始化库中配置定时任务信息
[32m2024-12-13 14:49:27.053[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.DynamicDataSourceTest - Started DynamicDataSourceTest in 12.861 seconds (JVM running for 13.564)
[32m2024-12-13 15:26:56.821[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.DynamicDataSourceTest - Starting DynamicDataSourceTest on pumpkin with PID 16132 (started by 35382 in D:\IdeaWorkspace\AppTools\Auditreport)
[32m2024-12-13 15:26:56.821[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.DynamicDataSourceTest - The following profiles are active: dev
[32m2024-12-13 15:27:00.378[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.Application - =======================additional config load successfully: loaded-ai-version01
[32m2024-12-13 15:27:04.743[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.config.MultipartConfig - 文件上传临时路径:C:\Users\<USER>\tmp
[32m2024-12-13 15:27:06.982[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.schedule.impl.CronTaskInitRunner - 初始化库中配置定时任务信息
[32m2024-12-13 15:27:06.982[0;39m [[34mmain[0;39m] [34mINFO [0;39m com.suke.czx.DynamicDataSourceTest - Started DynamicDataSourceTest in 10.519 seconds (JVM running for 11.185)
