2024-12-13 14:49:26,794 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.qryAllAuditDictByActived [159] - ==>  Preparing: select DICT_ID,DICT_TYPE_ID,DICT_VALUE,DICT_NAME,DICT_DESC,STATUS,CREATE_DATE,CREATE_PERSON,MODIFY_DATE,MODIFY_PERSON,CRON_EXPRESSION from PUBLIC_RPT_DICT where STATUS='1' order by DICT_ID desc 
2024-12-13 14:49:26,803 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.qryAllAuditDictByActived [159] - ==> Parameters: 
2024-12-13 14:49:26,907 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.qryAllAuditDictByActived [159] - <==      Total: 13
2024-12-13 14:49:26,918 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.queryRptCheckCfgByRuleIdWithActived [159] - ==>  Preparing: select RULE_ID,RULE_NAME,STATUS,CREATE_PERSON,CREATE_DATE,MODIFY_PERSON,MODIFY_DATE,CHK_TYPE_ID,CENTER_ID,MODLE_ID,RULE_SQL,LIMIT_VALUE,RULE_DESC,ENV_TYPE from RPT_CHECK_CFG where MODLE_ID = ? and STATUS = '1' 
2024-12-13 14:49:26,921 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.queryRptCheckCfgByRuleIdWithActived [159] - ==> Parameters: 3003(Integer)
2024-12-13 14:49:27,044 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.queryRptCheckCfgByRuleIdWithActived [159] - <==      Total: 1
2024-12-13 14:49:27,049 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.queryRptCheckCfgByRuleIdWithActived [159] - ==>  Preparing: select RULE_ID,RULE_NAME,STATUS,CREATE_PERSON,CREATE_DATE,MODIFY_PERSON,MODIFY_DATE,CHK_TYPE_ID,CENTER_ID,MODLE_ID,RULE_SQL,LIMIT_VALUE,RULE_DESC,ENV_TYPE from RPT_CHECK_CFG where MODLE_ID = ? and STATUS = '1' 
2024-12-13 14:49:27,049 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.queryRptCheckCfgByRuleIdWithActived [159] - ==> Parameters: 3008(Integer)
2024-12-13 14:49:27,051 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.queryRptCheckCfgByRuleIdWithActived [159] - <==      Total: 1
2024-12-13 15:27:06,939 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.qryAllAuditDictByActived [159] - ==>  Preparing: select DICT_ID,DICT_TYPE_ID,DICT_VALUE,DICT_NAME,DICT_DESC,STATUS,CREATE_DATE,CREATE_PERSON,MODIFY_DATE,MODIFY_PERSON,CRON_EXPRESSION from PUBLIC_RPT_DICT where STATUS='1' order by DICT_ID desc 
2024-12-13 15:27:06,946 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.qryAllAuditDictByActived [159] - ==> Parameters: 
2024-12-13 15:27:06,959 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.qryAllAuditDictByActived [159] - <==      Total: 13
2024-12-13 15:27:06,965 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.queryRptCheckCfgByRuleIdWithActived [159] - ==>  Preparing: select RULE_ID,RULE_NAME,STATUS,CREATE_PERSON,CREATE_DATE,MODIFY_PERSON,MODIFY_DATE,CHK_TYPE_ID,CENTER_ID,MODLE_ID,RULE_SQL,LIMIT_VALUE,RULE_DESC,ENV_TYPE from RPT_CHECK_CFG where MODLE_ID = ? and STATUS = '1' 
2024-12-13 15:27:06,967 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.queryRptCheckCfgByRuleIdWithActived [159] - ==> Parameters: 3003(Integer)
2024-12-13 15:27:06,976 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.queryRptCheckCfgByRuleIdWithActived [159] - <==      Total: 1
2024-12-13 15:27:06,979 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.queryRptCheckCfgByRuleIdWithActived [159] - ==>  Preparing: select RULE_ID,RULE_NAME,STATUS,CREATE_PERSON,CREATE_DATE,MODIFY_PERSON,MODIFY_DATE,CHK_TYPE_ID,CENTER_ID,MODLE_ID,RULE_SQL,LIMIT_VALUE,RULE_DESC,ENV_TYPE from RPT_CHECK_CFG where MODLE_ID = ? and STATUS = '1' 
2024-12-13 15:27:06,980 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.queryRptCheckCfgByRuleIdWithActived [159] - ==> Parameters: 3008(Integer)
2024-12-13 15:27:06,981 [main] DEBUG com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper.queryRptCheckCfgByRuleIdWithActived [159] - <==      Total: 1
