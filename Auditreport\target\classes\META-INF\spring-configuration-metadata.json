{"hints": [], "groups": [{"sourceType": "com.suke.czx.config.FtpProperties", "name": "ftp", "type": "com.suke.czx.config.FtpProperties"}, {"sourceType": "com.suke.czx.modules.app.utils.JwtUtils", "name": "jwtconfig.jwt", "type": "com.suke.czx.modules.app.utils.JwtUtils"}, {"sourceType": "com.suke.czx.datasources.DynamicDataSourceConfig", "name": "spring.datasource.druid.first", "sourceMethod": "firstDataSource()", "type": "javax.sql.DataSource"}, {"sourceType": "com.suke.czx.datasources.DynamicDataSourceConfig", "name": "spring.datasource.druid.second", "sourceMethod": "secondDataSource()", "type": "javax.sql.DataSource"}], "properties": [{"sourceType": "com.suke.czx.config.FtpProperties", "name": "ftp.ai-path", "type": "java.lang.String"}, {"sourceType": "com.suke.czx.config.FtpProperties", "name": "ftp.base-path", "type": "java.lang.String"}, {"sourceType": "com.suke.czx.config.FtpProperties", "name": "ftp.com-path", "type": "java.lang.String"}, {"sourceType": "com.suke.czx.config.FtpProperties", "name": "ftp.container-name", "type": "java.lang.String"}, {"sourceType": "com.suke.czx.config.FtpProperties", "name": "ftp.final-train-path", "description": "最终的训练文件路径", "type": "java.lang.String"}, {"sourceType": "com.suke.czx.config.FtpProperties", "name": "ftp.host", "type": "java.lang.String"}, {"sourceType": "com.suke.czx.config.FtpProperties", "name": "ftp.log-name", "type": "java.lang.String"}, {"sourceType": "com.suke.czx.config.FtpProperties", "name": "ftp.log-path", "type": "java.lang.String"}, {"sourceType": "com.suke.czx.config.FtpProperties", "name": "ftp.mt-abnormal-path", "description": "异常训练数据保存路径", "type": "java.lang.String"}, {"sourceType": "com.suke.czx.config.FtpProperties", "name": "ftp.mt-normal-path", "description": "正常训练数据保存路径", "type": "java.lang.String"}, {"sourceType": "com.suke.czx.config.FtpProperties", "name": "ftp.password", "type": "java.lang.String"}, {"sourceType": "com.suke.czx.config.FtpProperties", "defaultValue": 0, "name": "ftp.port", "type": "java.lang.Integer"}, {"sourceType": "com.suke.czx.config.FtpProperties", "name": "ftp.python-path", "type": "java.lang.String"}, {"sourceType": "com.suke.czx.config.FtpProperties", "name": "ftp.pythonbase-path", "type": "java.lang.String"}, {"sourceType": "com.suke.czx.config.FtpProperties", "name": "ftp.rule-path", "type": "java.lang.String"}, {"sourceType": "com.suke.czx.config.FtpProperties", "name": "ftp.user", "type": "java.lang.String"}, {"sourceType": "com.suke.czx.modules.app.utils.JwtUtils", "defaultValue": 0, "name": "jwtconfig.jwt.expire", "type": "java.lang.Long"}, {"sourceType": "com.suke.czx.modules.app.utils.JwtUtils", "name": "jwtconfig.jwt.header", "type": "java.lang.String"}, {"sourceType": "com.suke.czx.modules.app.utils.JwtUtils", "name": "jwtconfig.jwt.secret", "type": "java.lang.String"}]}