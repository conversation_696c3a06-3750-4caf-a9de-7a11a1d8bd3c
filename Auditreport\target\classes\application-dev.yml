spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
#        driverClassName: com.mysql.cj.jdbc.Driver
#        dbType: mysql
        druid:
            first:  #数据源1
                #                开发库
                name: bossmain1
                username: BOSSMAIN
                password: Greatdb2023
                url: **********************************************************************************************************************************************
                driverClassName: com.mysql.cj.jdbc.Driver
                dbType: mysql
            second:  #数据源2
                #                开发库
                url: *********************************************
                username: E<PERSON>(4v36lf6qirZOAWWtJ/ZtxWbOCbyfUHYV)
                password: ENC(kh8Kmu0zJGwdyI1TvYC26ya0VWsuxT//)
                driverClassName: oracle.jdbc.driver.OracleDriver
                dbType: oracle
            initial-size: 10
            max-active: 100
            min-idle: 10
            max-wait: 60000
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query: SELECT 1 FROM DUAL
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            stat-view-servlet:
                enabled: true
                url-pattern: /druid/*
                #login-username: admin
                #login-password: admin
            filter:
                stat:
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true

# 文件上传的服务器信息配置
# 开发主机
ftp:
    host: **************
    port: 22
    user: ENC(FtR7fEVbW5a1vU/9Z13+cA==)
    password: ENC(WQeIQZeD3O2ccwP22kCf12NJm3nCtLyg)
    basePath: /home/<USER>/liuning/audit/report
    comPath: /home/<USER>/liuning/audit/comparison
    rulePath: /home/<USER>/liuning/audit/rule
    aiPath: /home/<USER>/liuning/ai
    pythonbasePath: /home/<USER>/liuning/python-script/pythonProject1
#    pythonbasePath: D:\\PycharmWorkspace\\pythonProject1\\
    #    输出文件的位置
    logPath: /home/<USER>/liuning/audit
    logName: nohup.out
    containerName: audit-project
    mtNormalPath: /home/<USER>/liuning/audit/normalpath
    mtAbnormalPath: /home/<USER>/liuning/audit/abnormalpath
    finalTrainPath: /home/<USER>/liuning/audit/finalTrainPath

custom:
    property: loaded-ai-version01
upload:
    path: ./upload/
