# Tomcat
server:
    tomcat:
        uri-encoding: UTF-8
        max-threads: 1000
        min-spare-threads: 30
    port: 6008
    connection-timeout: 5000
    context-path: /x_springboot
#    文件上传


spring:
#     环境 dev|test|pro
    profiles:
        active: dev
    # jackson时间格式化
    jackson:
        time-zone: GMT+8
        date-format: yyyy-MM-dd HH:mm:ss
    http:
        multipart:
            max-file-size: 100MB
            max-request-size: 100MB
            enabled: true
    resources: # 指定静态资源的路径
        static-locations: classpath:/static/,classpath:/views/
    redis:
        open: false  # 是否开启redis缓存  true开启   false关闭
        database: 0
        host: localhost
        port: 16379
        password:       # 密码（默认为空）
        timeout: 6000  # 连接超时时长（毫秒）
        pool:
            max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
            max-wait: -1      # 连接池最大阻塞等待时间（使用负值表示没有限制）
            max-idle: 10      # 连接池中的最大空闲连接
            min-idle: 5       # 连接池中的最小空闲连接

# Mybatis配置
mybatis:
    mapperLocations: classpath:mapper/**/*.xml
    configLocation: classpath:mybatis.xml

jwtconfig:
    # APP模块，是通过jwt认证的，如果要使用APP模块，则需要修改【加密秘钥】
    jwt:
        # 加密秘钥
        secret: f4e2e52034348f86b67cde581c0f9eb5
        # token有效时长，7天，单位秒
        expire: 604800
        header: token

# quartz定时任务
job:
    scheduling:
        enabled: false
custom:
    property: loaded-default
# jasypt对配置文件信息加密
jasypt:
    encryptor:
        password: Nlp1US8sk9nTL0jh8HSYisi64jgoi569

upload:
    path: ./upload/