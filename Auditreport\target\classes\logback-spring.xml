<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 默认的一些配置 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <!-- 定义应用名称，区分应用 -->
    <property name="APP_NAME" value="audit-report"/>
    <!-- 定义日志文件的输出路径 -->
    <property name="LOG_PATH" value="./logs/%d{yyyyMMdd}"/>

    <!-- 自定义控制台打印格式 -->
    <property name="FILE_LOG_PATTERN" value="%green(%d{yyyy-MM-dd HH:mm:ss.SSS}) [%highlight(%thread)] %highlight(%-5level) %logger{50} - %msg%n"/>



    <!-- 将日志滚动输出到application.log文件中 -->
    <appender name="APPLICATION" class="ch.qos.logback.core.rolling.RollingFileAppender">

        <encoder>
            <!-- 使用默认的输出格式打印 -->
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>

        <!-- 设置 RollingPolicy 属性，用于配置文件大小限制，保留天数、文件名格式 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 文件命名格式 -->
            <fileNamePattern>${LOG_PATH}/application.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 文件保留最大天数 -->
            <maxHistory>7</maxHistory>
            <!-- 文件大小限制 -->
            <maxFileSize>10MB</maxFileSize>
            <!-- 文件总大小 -->
            <totalSizeCap>100MB</totalSizeCap>
        </rollingPolicy>
    </appender>


    <!-- 摘取出WARN级别日志输出到warn.log中 -->
    <appender name="WARN" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <!-- 使用默认的输出格式打印 -->
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
        <!-- 设置 RollingPolicy 属性，用于配置文件大小限制，保留天数、文件名格式 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 文件命名格式 -->
            <fileNamePattern>${LOG_PATH}/warn.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 文件保留最大天数 -->
            <maxHistory>7</maxHistory>
            <!-- 文件大小限制 -->
            <maxFileSize>10MB</maxFileSize>
            <!-- 文件总大小 -->
            <totalSizeCap>100MB</totalSizeCap>
        </rollingPolicy>
        <!-- 日志过滤器，将WARN相关日志过滤出来 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
    </appender>

    <!-- 摘取出ERROR级别日志输出到error.log中 -->
    <appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <!-- 使用默认的输出格式打印 -->
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
        <!-- 设置 RollingPolicy 属性，用于配置文件大小限制，保留天数、文件名格式 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 文件命名格式 -->
            <fileNamePattern>${LOG_PATH}/error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 文件保留最大天数 -->
            <maxHistory>7</maxHistory>
            <!-- 文件大小限制 -->
            <maxFileSize>10MB</maxFileSize>
            <!-- 文件总大小 -->
            <totalSizeCap>100MB</totalSizeCap>
        </rollingPolicy>
        <!-- 日志过滤器，将ERROR相关日志过滤出来 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <!-- 摘取出ERROR级别日志输出到Debug.log中 -->
    <appender name="DEBUG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <!-- 使用默认的输出格式打印 -->
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
        <!-- 设置 RollingPolicy 属性，用于配置文件大小限制，保留天数、文件名格式 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 文件命名格式 -->
            <fileNamePattern>${LOG_PATH}/debug.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 文件保留最大天数 -->
            <maxHistory>7</maxHistory>
            <!-- 文件大小限制 -->
            <maxFileSize>10MB</maxFileSize>
            <!-- 文件总大小 -->
            <totalSizeCap>100MB</totalSizeCap>
        </rollingPolicy>
        <!-- 日志过滤器，将ERROR相关日志过滤出来 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>


    <!-- 配置控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <!-- 配置日志打印格式 -->
            <!--            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] ${PID:- } %logger{36} %-5level - %msg%n</pattern>-->
            <!-- 使用默认的输出格式打印 -->
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <!-- 定义指定目录service的appender -->
    <appender name="SERVICE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <!-- 使用默认的输出格式打印 -->
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
        <!-- 设置 RollingPolicy 属性，用于配置文件大小限制，保留天数、文件名格式 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 文件命名格式 -->
            <fileNamePattern>${LOG_PATH}/service.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 文件保留最大天数 -->
            <maxHistory>7</maxHistory>
            <!-- 文件大小限制 -->
            <maxFileSize>10MB</maxFileSize>
            <!-- 文件总大小 -->
            <totalSizeCap>100MB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!--    打印 sql  -->
    <appender name="APP_SQL" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <!-- onMatch:意思是当前级别以及以上要怎么处理 -->
            <onMatch>ACCEPT</onMatch>
            <!-- onMismatch:意思是当前级别(不包括当前级别)以下要怎么处理 -->
            <onMismatch>DENY</onMismatch>
        </filter>
        <!--存档日志示例-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/sql.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!--日志大小可自定义-->
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!--存档天数可自定义-->
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <!--统一日志输出格式-->
        <encoder charset="UTF-8">
            <pattern>%date [%thread] %-5level %logger [%L] - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 配置扫描包路径，追加日志到service的appender中 -->
    <!--若是additivity设为true，则子Logger不止会在自己的appender里输出，还会在root的logger的appender里输出-->
    <logger name="com.suke.czx" level="INFO" additivity="true">
        <appender-ref ref="SERVICE"/>
    </logger>

    <!--
        <logger>用来设置某一个包或者具体的某一个类的日志打印级别、以及指定<appender>。
        <logger>仅有一个name属性，
        一个可选的level和一个可选的addtivity属性。
        name:用来指定受此logger约束的某一个包或者具体的某一个类。
        level:用来设置打印级别，大小写无关：TRACE, DEBUG, INFO, WARN, ERROR, ALL 和 OFF，
              如果未设置此属性，那么当前logger将会继承上级的级别。
    -->
    <!--
        使用mybatis的时候，sql语句是debug下才会打印，而这里我们只配置了info，所以想要查看sql语句的话，有以下两种操作：
        第一种把<root level="INFO">改成<root level="DEBUG">这样就会打印sql，不过这样日志那边会出现很多其他消息
        第二种就是单独给mapper下目录配置DEBUG模式，代码如下，这样配置sql语句会打印，其他还是正常DEBUG级别：
     -->
    <springProfile name="dev,test">
        <!--可以输出项目中的debug日志，包括mybatis的sql日志-->
        <logger name="com.suke.czx.newland.dao" level="DEBUG" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="APP_SQL"/>
        </logger>
    </springProfile>
    <springProfile name="pro">
        <logger name="com.suke.czx.newland.dao" level="DEBUG" additivity="false">
            <appender-ref ref="APP_SQL"/>
        </logger>
    </springProfile>

    <!-- 配置输出级别，加入输出方式 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <!-- 加入APPLICATION输出 -->
        <appender-ref ref="APPLICATION"/>
        <!-- 加入WARN日志输出 -->
        <appender-ref ref="WARN"/>
        <!-- 加入ERROR日志输出 -->
        <appender-ref ref="ERROR"/>
        <!-- 加入ERROR日志输出 -->
        <appender-ref ref="DEBUG"/>
    </root>
</configuration>