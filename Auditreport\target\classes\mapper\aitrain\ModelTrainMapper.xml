<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.suke.czx.modules.aitrain.mapper.ModelTrainMapper">
    <insert id="insert">
        INSERT INTO ai_model_train (train_name, model, score_test, eval_metric, fit_time, can_infer, fit_order, remark)
        VALUES
            (#{trainName}, #{model}, #{scoreTest}, #{evalMetric}, #{fitTime}, #{canInfer}, #{fitOrder}, #{remark})
    </insert>
    <insert id="batchInsert" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            INSERT INTO ai_model_train (train_name, model, score_test, eval_metric, fit_time, can_infer, fit_order, remark)
            VALUES (#{item.trainName},
            #{item.model},
            #{item.scoreTest},
            #{item.evalMetric},
            #{item.fitTime},
            #{item.canInfer},
            #{item.fitOrder},
            #{item.remark})
        </foreach>
    </insert>
    <update id="update">
        UPDATE ai_model_train
        SET
        <if test="trainName != null and trainName != ''">
            train_name = #{trainName},
        </if>
        <if test="model != null and model != ''">
            model = #{model},
        </if>
        <if test="scoreTest != null">
            score_test = #{scoreTest},
        </if>
        <if test="evalMetric != null and evalMetric != ''">
            eval_metric = #{evalMetric},
        </if>
        <if test="fitTime != null">
            fit_time = #{fitTime},
        </if>
        <if test="canInfer != null and canInfer != ''">
            can_infer = #{canInfer},
        </if>
        <if test="fitOrder != null">
            fit_order = #{fitOrder},
        </if>
        <if test="remark != null and remark != ''">
            remark = #{remark},
        </if>
        WHERE
        id = #{id}
    </update>
    <delete id="deleteById">
        delete from ai_model_train where id = #{id}
    </delete>


    <select id="getAll" resultType="com.suke.czx.modules.aitrain.entity.ModelTrain">
        select id,train_name,model,score_test,eval_metric,fit_time,can_infer,fit_order,create_time from ai_model_train
    </select>
    <select id="getTrainNameList" resultType="java.lang.String">
        SELECT train_name FROM ai_model_train group by train_name
    </select>
    <select id="queryByTrainName" resultType="com.suke.czx.modules.aitrain.entity.ModelTrain">
        select id,train_name,model,score_test,eval_metric,fit_time,can_infer,fit_order,remark,create_time from ai_model_train where train_name = #{trainName}
    </select>
    <select id="queryAllTrainName" resultType="java.lang.String">
        SELECT
            t.train_name
        FROM (
                 SELECT
                     train_name,
                     MAX(create_time) AS max_create_time
                 FROM
                     ai_model_train
                 GROUP BY
                     train_name
             ) t
        ORDER BY
            t.max_create_time DESC
    </select>
    <select id="getModelSum" resultType="java.lang.Integer">
        SELECT count(1) FROM (select train_name from ai_model_train group by train_name)
    </select>
    <select id="getModelByTrainName" resultType="java.lang.String">
        SELECT model FROM ai_model_train where train_name=#{trainName}
    </select>
    <select id="getScoreTestByTrainName" resultType="java.lang.Double">
        SELECT score_test FROM ai_model_train where train_name=#{trainName}
    </select>
    <select id="getFitTimeByTrainName" resultType="java.lang.Double">
        SELECT fit_time FROM ai_model_train where train_name=#{trainName}
    </select>
</mapper>