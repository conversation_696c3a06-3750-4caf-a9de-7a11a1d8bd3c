<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper">

    <insert id="addAuditConfig" parameterType="com.suke.czx.newland.vo.audit.AuditConfigVo">
    INSERT INTO RPT_CHECK_CFG (RULE_ID,
                               RULE_NAME,
                               STATUS,
                               CREATE_PERSON,
                               CREATE_DATE,
                               MODIFY_PERSON,
                               MODIFY_DATE,
                               CHK_TYPE_ID,
                               CENTER_ID,
                               MODLE_ID,
                               RULE_SQL,
                               LIMIT_VALUE,
                               RULE_DESC,
                               ENV_TYPE)
    VALUES (#{ruleId},
            #{ruleName},
            #{status},
            #{create<PERSON>erson},
            NOW(),
            NULL,
            NULL,
            #{chkTypeId},
            #{centerId},
            #{modleId},
            #{ruleSql},
            #{limitValue},
            #{ruleDesc},
            #{envType})
    </insert>

    <insert id="addAuditDict">
    INSERT INTO PUBLIC_RPT_DICT (DICT_ID,
                                 DICT_TYPE_ID,
                                 DICT_VALUE,
                                 DICT_NAME,
                                 DICT_DESC,
                                 STATUS,
                                 CREATE_DATE,
                                 CREATE_PERSON,
                                 MODIFY_DATE,
                                 MODIFY_PERSON)
    VALUES (#{dictId},
            #{dictTypeId},
            #{dictValue},
            #{dictName},
            #{dictDesc},
            #{status},
            NOW(),
            #{createPerson},
            NOW(),
            #{createPerson})
    </insert>


    <delete id="delAuditDictByDictId" parameterType="java.lang.Integer">
        delete
        from PUBLIC_RPT_DICT
        where DICT_ID = #{dictId}
    </delete>
    <delete id="delAuditConfig">
        delete
        from RPT_CHECK_CFG
        where RULE_ID = #{ruleId}
    </delete>
    <delete id="deleteHistoryByDate">

        <![CDATA[
    DELETE FROM RPT_CHECK_LOG WHERE EXEC_DATE < NOW() - INTERVAL 9 MONTH
      ]]>
    </delete>

    <select id="selectCountByXuhao" resultType="java.lang.Long">
         SELECT IFNULL(MAX(RULE_ID), -1)
         FROM RPT_CHECK_CFG
    </select>
    <insert id="addAuditType" parameterType="com.suke.czx.newland.vo.audit.AuditDictTypeVo">
    INSERT INTO PUBLIC_DICT_TYPE (
        DICT_TYPE_ID,
        DICT_TYPE_NAME,
        DICT_TYPE_DESC,
        CREATE_DATE,
        CREATE_PERSON,
        MODIFY_DATE,
        MODIFY_PERSON
    ) VALUES (
                 #{dictTypeId},
                 #{dictTypeName},
                 #{dictTypeDesc},
                 NOW(),
                 #{createPerson},
                 NOW(),
                 #{modifyPerson}
             )
    </insert>
    <insert id="bakAuditCfgByRuleId">
        PREPARE stmt FROM 'INSERT INTO RPT_CHECK_CFG_HIS SELECT * FROM RPT_CHECK_CFG WHERE rule_id = ?';
        SET
        @ruleIdValue = ?; -- 这里?是一个示意，实际在应用程序中不会这样设置，而是直接在执行预处理语句时传递
        EXECUTE stmt USING @ruleIdValue; -- 但更好的做法是直接在执行时传递参数，而不是先设置变量
        DEALLOCATE PREPARE stmt
    </insert>
    <select id="queryDictType" resultType="com.suke.czx.newland.vo.audit.AuditDictTypeVo">
        select DICT_TYPE_ID,DICT_TYPE_NAME,DICT_TYPE_DESC,CREATE_DATE,CREATE_PERSON,MODIFY_DATE,MODIFY_PERSON
        from PUBLIC_DICT_TYPE order by DICT_TYPE_ID desc
    </select>
    <select id="queryDictByType" resultType="com.suke.czx.newland.vo.audit.AuditDictVo" parameterType="java.lang.Integer">
        select DICT_ID,
               DICT_TYPE_ID,
               DICT_VALUE,
               DICT_NAME,
               DICT_DESC,
               STATUS,
               CREATE_DATE,
               CREATE_PERSON,
               MODIFY_DATE,
               MODIFY_PERSON
        from PUBLIC_RPT_DICT
        where DICT_TYPE_ID = #{typeId}
        order by DICT_ID desc
    </select>
    <select id="selectMaxId" resultType="java.lang.Integer">
        SELECT IFNULL(MAX(RULE_ID), -1)
        FROM PUBLIC_RPT_DICT
    </select>

    <select id="selectDictValMaxId" resultType="java.lang.Integer">
        SELECT IFNULL(MAX(DICT_VALUE), -1)
        FROM PUBLIC_RPT_DICT
    </select>

    <select id="queryAuditConfig" resultType="com.suke.czx.newland.vo.audit.AuditConfigVo">
        select RULE_ID,RULE_NAME,STATUS,CREATE_PERSON,CREATE_DATE,MODIFY_PERSON,MODIFY_DATE,CHK_TYPE_ID,CENTER_ID,MODLE_ID,RULE_SQL,LIMIT_VALUE,RULE_DESC,ENV_TYPE from RPT_CHECK_CFG where 1=1
        <if test="key != '' and key != null">
            and (RULE_SQL like concat(concat('%',#{key}),'%') or RULE_NAME like concat(concat('%',#{key}),'%'))
        </if>
        <if test="value != '' and value != null">
            and CHK_TYPE_ID = #{value}
        </if>
        <if test="center != '' and center != null">
            and CENTER_ID = #{center}
        </if>
        <if test="modle != '' and modle != null">
            and MODLE_ID = #{modle}
        </if>
        <if test="modifyDate != null and endTime != null">
            AND MODIFY_DATE BETWEEN #{modifyDate} AND #{endTime}
        </if>
        <if test="status != -1">
            and status = #{status}
        </if>
        <if test="runDatasourceId != null and runDatasourceId != ''">
            and ENV_TYPE = #{runDatasourceId}
        </if>
        order by RULE_ID desc
    </select>

    <select id="queryAuditLogByAll" resultType="com.suke.czx.newland.vo.audit.AuditLogVo" parameterType="com.suke.czx.newland.vo.audit.AuditLogVo">
        select log.LOG_ID        logId,
        log.DB_NAME       dbName,
        log.DB_MACHINE    dbMachine,
        log.RULE_ID       ruleId,
        log.MODIFY_PERSON modifyPerson,
        log.MODIFY_DATE   modifyDate,
        log.CHK_TYPE_ID   chkTypeId,
        log.CHK_TYPE_NAME chkTypeName,
        log.CENTER_ID     centerId,
        log.CENTER_NAME   centerName,
        log.MODLE_ID      modleId,
        log.MODLE_NAME    modleName,
        log.RULE_NAME     ruleName,
        log.RULE_SQL      ruleSql,
        log.LIMIT_VALUE   limitValue,
        log.SQL_VALUE     sqlValue,
        log.STATUS        status,
        log.CONCLUSION    conclusion,
        log.EXEC_MSEC     execMsec,
        log.EXEC_REMARK   execRemark,
        log.CREATE_DATE   createDate,
        log.CREATE_PERSON createPerson,
        log.EXEC_DATE     execDate,
        log.EXEC_PERSON   execPerson,
        log.ANALYSIS_RES  analysisRes
        from RPT_CHECK_LOG log left join RPT_CHECK_CFG cfg on log.RULE_ID = cfg.RULE_ID where 1=1
        <if test="condition.ruleName != null and condition.ruleName != ''">
            and log.RULE_NAME like concat(concat('%',#{condition.ruleName}),'%')
        </if>
        <if test="condition.centerId != null and condition.centerId != ''">
            and log.CENTER_ID = #{condition.centerId}
        </if>
        <if test="condition.modleId != null and condition.modleId != ''">
            and log.MODLE_ID = #{condition.modleId}
        </if>
        <if test="condition.chkTypeId != null and condition.chkTypeId != ''">
            and log.CHK_TYPE_ID = #{condition.chkTypeId}
        </if>
        <if test="condition.createDate != null and condition.endTime != null" >
            AND MODIFY_DATE BETWEEN #{modifyDate} AND #{endTime}
        </if>
        <if test="condition.conclusion != null and condition.conclusion != ''">
            and log.CONCLUSION = #{condition.conclusion,jdbcType = VARCHAR}
        </if>
        <if test="condition.analysisRes != null and condition.analysisRes != ''">
            and log.ANALYSIS_RES = #{condition.analysisRes}
        </if>
        <if test="datasourceId != null and datasourceId != ''">
            and cfg.ENV_TYPE = #{datasourceId}
        </if>
        order by LOG_ID desc
    </select>

    <select id="selectAuditConfigCountByTypeId" resultType="java.lang.Integer">
        select count(1) from RPT_CHECK_CFG where CHK_TYPE_ID = #{typeId} or CENTER_ID =  #{typeId} or MODLE_ID =  #{typeId}
    </select>

    <select id="callAuditProcedure" statementType="CALLABLE" resultType="java.lang.String">
        {call p_report_audit_config_new(#{cfgId, jdbcType=INTEGER, mode=IN}, #{execPerson, jdbcType=VARCHAR, mode=IN})}
    </select>
    <select id="execSqlByRuleId" parameterType="java.lang.String" resultType="java.lang.Integer">
        ${ruleSql}
    </select>

<!--    <select id="queryHisConfigByRuleId" resultType="com.newland.vo.audit.AuditConfigVo">-->
<!--        select RULE_ID,RULE_NAME,STATUS,CREATE_PERSON,CREATE_DATE,MODIFY_PERSON,MODIFY_DATE,CHK_TYPE_ID,CENTER_ID,MODLE_ID,RULE_SQL,LIMIT_VALUE,RULE_DESC,ENV_TYPE from RPT_CHECK_CFG_HIS where rule_id = #{ruleId} order by modify_date desc nulls last-->
<!--    </select>-->

    <update id="editAuditDict">
        update PUBLIC_RPT_DICT set DICT_VALUE = #{dictValue}, DICT_TYPE_ID = #{dictTypeId},DICT_NAME = #{dictName},DICT_DESC = #{dictDesc},STATUS = #{status},MODIFY_DATE = NOW(),MODIFY_PERSON = #{modifyPerson},CRON_EXPRESSION = #{cronExpression}
                               where DICT_ID = #{dictId}
    </update>
    <update id="editAuditConfig">
        update RPT_CHECK_CFG set RULE_NAME = #{ruleName},MODIFY_PERSON = #{createPerson},MODIFY_DATE = NOW(),CHK_TYPE_ID = #{chkTypeId},CENTER_ID = #{centerId},MODLE_ID = #{modleId},RULE_SQL = #{ruleSql},LIMIT_VALUE = #{limitValue},RULE_DESC = #{ruleDesc},STATUS = #{status},ENV_TYPE=#{envType}
                             where RULE_ID = #{ruleId}
    </update>
    <update id="updateHisToryByDate">
        UPDATE RPT_CHECK_LOG
        SET STATUS = 2
        WHERE DATE_FORMAT(IFNULL(EXEC_DATE, NOW()), '%Y%m%d') = DATE_FORMAT(NOW(), '%Y%m%d')
    </update>
    <update id="saveExecRemarkByLogId" parameterType="com.suke.czx.newland.vo.audit.AuditLogVo">
        update RPT_CHECK_LOG set exec_remark = #{execRemark} where log_id = #{logId}
    </update>
    <update id="saveAnalysisResByLogId" parameterType="com.suke.czx.newland.vo.audit.AuditLogVo">
        update RPT_CHECK_LOG set ANALYSIS_RES= #{analysisRes} where log_id = #{logId}
    </update>
    <insert id="importResultData" parameterType="java.util.List" useGeneratedKeys="false">
        INSERT INTO RPT_CHECK_LOG (
        LOG_ID,
        DB_NAME,
        DB_MACHINE,
        RULE_ID,
        MODIFY_PERSON,
        MODIFY_DATE,
        CHK_TYPE_ID,
        CHK_TYPE_NAME,
        CENTER_ID,
        CENTER_NAME,
        MODLE_ID,
        MODLE_NAME,
        RULE_NAME,
        RULE_SQL,
        LIMIT_VALUE,
        SQL_VALUE,
        STATUS,
        CONCLUSION,
        EXEC_MSEC,
        EXEC_REMARK,
        CREATE_DATE,
        CREATE_PERSON,
        EXEC_DATE,
        EXEC_PERSON,
        ANALYSIS_RES
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.logId},
            #{item.dbName},
            #{item.dbMachine},
            #{item.ruleId},
            #{item.modifyPerson},
            #{item.modifyDate},
            #{item.chkTypeId},
            #{item.chkTypeName},
            #{item.centerId},
            #{item.centerName},
            #{item.modleId},
            #{item.modleName},
            #{item.ruleName},
            #{item.ruleSql},
            #{item.limitValue},
            #{item.sqlValue},
            #{item.status},
            #{item.conclusion},
            #{item.execMsec},
            #{item.execRemark},
            #{item.createDate},
            #{item.createPerson},
            #{item.execDate},
            #{item.execPerson},
            #{item.analysisRes}
            )
        </foreach>
    </insert>

    <insert id="importAuditData" parameterType="java.util.List" useGeneratedKeys="false">
        INSERT INTO RPT_CHECK_CFG (
        RULE_ID,
        RULE_NAME,
        STATUS,
        CREATE_PERSON,
        CREATE_DATE,
        MODIFY_PERSON,
        MODIFY_DATE,
        CHK_TYPE_ID,
        CENTER_ID,
        MODLE_ID,
        RULE_SQL,
        LIMIT_VALUE,
        RULE_DESC,
        ENV_TYPE
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.ruleId},
            #{item.ruleName},
            #{item.status},
            #{item.createPerson},
            #{item.createDate},
            #{item.modifyPerson},
            #{item.modifyDate},
            #{item.chkTypeId},
            #{item.centerId},
            #{item.modleId},
            #{item.ruleSql},
            #{item.limitValue},
            #{item.ruleDesc},
            #{item.envType}
            )
        </foreach>
    </insert>


    <select id="queryHisConfigByRuleId" resultType="com.suke.czx.newland.vo.audit.AuditConfigVo">

        SELECT RULE_ID, RULE_NAME, STATUS, CREATE_PERSON, CREATE_DATE, MODIFY_PERSON, MODIFY_DATE, CHK_TYPE_ID, CENTER_ID, MODLE_ID, RULE_SQL, LIMIT_VALUE, RULE_DESC, ENV_TYPE
        FROM (
        SELECT RULE_ID, RULE_NAME, STATUS, CREATE_PERSON, CREATE_DATE, MODIFY_PERSON, MODIFY_DATE, CHK_TYPE_ID, CENTER_ID, MODLE_ID, RULE_SQL, LIMIT_VALUE, RULE_DESC, ENV_TYPE
        FROM RPT_CHECK_CFG_HIS
        WHERE rule_id = #{ruleId}
        <if test="modifyPerson != '' and modifyPerson != null">
            AND MODIFY_PERSON LIKE CONCAT('%', #{modifyPerson, jdbcType=VARCHAR}, '%')
        </if>
        <if test="ruleName != '' and ruleName != null">
            AND RULE_NAME LIKE CONCAT('%', #{ruleName, jdbcType=VARCHAR}, '%')
        </if>
        ORDER BY MODIFY_DATE DESC, MODIFY_DATE IS NULL ASC  -- MySQL does not support 'nulls last' directly, so we use a trick
        ) AS subquery
        LIMIT 1
    </select>

    <select id="queryJobStatus" resultType="java.util.Map">
        select DATE_FORMAT(next_date, '%Y%m%d') AS NEXT_DATE,
               FAILURES
        from dba_jobs
        where JOB = '469'
    </select>


    <!--首页度量查询语句-->
    <select id="queryDataDistribution" resultType="java.util.Map">
        SELECT t.MODLE_ID AS MODLEID,
               t1.DICT_NAME AS NAME,
               COUNT(*) AS VALUE
        FROM RPT_CHECK_CFG t
            JOIN PUBLIC_RPT_DICT t1 ON t1.DICT_VALUE = t.MODLE_ID
        WHERE t1.DICT_TYPE_ID = 12
        GROUP BY t1.DICT_NAME, t.MODLE_ID
    </select>

    <select id="qryAllAuditDictByActived" resultType="com.suke.czx.newland.vo.audit.AuditDictVo">
        select DICT_ID,DICT_TYPE_ID,DICT_VALUE,DICT_NAME,DICT_DESC,STATUS,CREATE_DATE,CREATE_PERSON,MODIFY_DATE,MODIFY_PERSON,CRON_EXPRESSION
        from PUBLIC_RPT_DICT where STATUS='1' order by DICT_ID desc
    </select>
    <select id="qryAllAuditDictByDictId" resultType="com.suke.czx.newland.vo.audit.AuditDictVo">
        select DICT_ID,DICT_TYPE_ID,DICT_VALUE,DICT_NAME,DICT_DESC,STATUS,CREATE_DATE,CREATE_PERSON,MODIFY_DATE,MODIFY_PERSON,CRON_EXPRESSION
        from PUBLIC_RPT_DICT where DICT_ID = #{dictId} and  STATUS='1' order by DICT_ID desc
    </select>

    <select id="queryRptCheckCfgByRuleIdWithActived" resultType="com.suke.czx.newland.vo.audit.AuditConfigVo">
        select RULE_ID,RULE_NAME,STATUS,CREATE_PERSON,CREATE_DATE,MODIFY_PERSON,MODIFY_DATE,CHK_TYPE_ID,CENTER_ID,MODLE_ID,RULE_SQL,LIMIT_VALUE,RULE_DESC,ENV_TYPE from RPT_CHECK_CFG where MODLE_ID = #{dictId} and STATUS = '1'
    </select>
    <select id="qryAuditDictByDictValue" resultType="com.suke.czx.newland.vo.audit.AuditDictVo">
        select DICT_ID,DICT_TYPE_ID,DICT_VALUE,DICT_NAME,DICT_DESC,STATUS,CREATE_DATE,CREATE_PERSON,MODIFY_DATE,MODIFY_PERSON,CRON_EXPRESSION
        from PUBLIC_RPT_DICT where DICT_VALUE = #{dictValue} and  STATUS='1' order by DICT_ID desc
    </select>

    <select id="qryAuditDictByDictValueWithoutStatus" resultType="com.suke.czx.newland.vo.audit.AuditDictVo">
        select DICT_ID,DICT_TYPE_ID,DICT_VALUE,DICT_NAME,DICT_DESC,STATUS,CREATE_DATE,CREATE_PERSON,MODIFY_DATE,MODIFY_PERSON,CRON_EXPRESSION
        from PUBLIC_RPT_DICT where DICT_VALUE = #{dictValue} order by DICT_ID desc
    </select>
    <select id="qryAuditCfgWithRuleIds" resultType="com.suke.czx.newland.vo.audit.AuditConfigVo">
        select RULE_ID,RULE_NAME,STATUS,CREATE_PERSON,CREATE_DATE,MODIFY_PERSON,MODIFY_DATE,CHK_TYPE_ID,CENTER_ID,MODLE_ID,RULE_SQL,LIMIT_VALUE,RULE_DESC,ENV_TYPE from RPT_CHECK_CFG where 1=1
        and RULE_ID in
            <foreach collection="array" item="ruleId" open="(" close=")" separator=",">
                #{ruleId}
            </foreach>
    </select>
    <select id="qryAuditDateWithDuration" resultType="com.suke.czx.newland.dto.CheckLogAnalysisDto">
        SELECT t.MODLE_NAME AS modleName,
               COUNT(*) AS countVal,
               t.ANALYSIS_RES AS analysisRes
        FROM RPT_CHECK_LOG t
        WHERE 1=1
          AND EXEC_DATE > NOW() - INTERVAL #{duration} DAY
        GROUP BY t.MODLE_NAME, t.ANALYSIS_RES
    </select>
</mapper>
