<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.suke.czx.modules.audit.dao.AuditComparisonDao">

    <insert id="saveComparison" parameterType="com.suke.czx.modules.audit.entity.AuditComparison">
        insert into audit_comparison (
        name,
        comparison_times,
        status,
        comparison_user_id,
        spend_time,
        url
        ) values (
        #{name},
        #{comparisonTimes},
        #{status},
        #{comparisonUserId},
        #{spendTime},
        #{url}
        )
    </insert>
    <!-- 根据文件名称删除比对信息表中的数据 -->
    <delete id="deleteByFileName">
        delete from audit_comparison where name = #{fileName}
    </delete>
    <!-- 根据id删除比对信息表中的数据 -->
    <delete id="delete">
        delete from audit_comparison where id = #{id}
    </delete>
    <!-- 根据fileName查询比对信息 -->
    <select id="queryObject" resultType="com.suke.czx.modules.audit.entity.AuditComparison">
        select * from audit_comparison where name = #{id}
    </select>
    <!-- 根据文件名查询比对信息表 -->
    <select id="queryByFileName" resultType="com.suke.czx.modules.audit.entity.AuditComparison">
        select * from audit_comparison where name = #{fileName}
    </select>

    <!-- 更新比对信息 -->
    <update id="update" parameterType="com.suke.czx.modules.audit.entity.AuditComparison">
        update audit_comparison
        <set>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="comparisonTimes != null and comparisonTimes != ''">comparison_times = #{comparisonTimes},</if>
            <if test="status != null">status = #{status,jdbcType=INTEGER},</if>
            <if test="comparisonUserId != null and comparisonUserId != ''">comparison_user_id = #{comparisonUserId},</if>
            <if test="spendTime != null and spendTime != ''">spend_time = #{spendTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="url != null">url = #{url}</if>
        </set>
        where id = #{id}
    </update>

    <select id="queryList" resultType="com.suke.czx.modules.audit.entity.AuditComparison">
        select * from audit_comparison
        <where>
            <if test="name != null and name.trim() != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="userId != null and userId.trim() != ''">
                and comparison_user_id like concat('%', #{userId}, '%')
            </if>
        </where>
        order by CREATE_TIME desc
        <if test="offset != null and limit != null">
            limit #{offset}, #{limit}
        </if>
    </select>
    <select id="queryListByIds" resultType="com.suke.czx.modules.audit.entity.AuditComparison">
        select * from audit_comparison
        where id in
        <foreach collection="array" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryAuditComparisonById" resultType="com.suke.czx.modules.audit.entity.AuditComparison">
        select * from audit_comparison where id = #{id}
    </select>
    <select id="queryByFileNameAndUserId" resultType="com.suke.czx.modules.audit.entity.AuditComparison">
        select * from audit_comparison where name = #{fileName} and COMPARISON_USER_ID = #{userId}
    </select>

    <delete id="deleteBatch">
        delete from audit_comparison where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>