<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.suke.czx.newland.dao.mgrdbmapper.AuditFileDatasourceRuleMapper">

    <insert id="addBatchAuditFileDatasourceRule" parameterType="java.util.List">
        insert into AUDIT_FILE_DATASOURCE_RULE ( RULE_NAME, AUDIT_FIELDS, AUDIT_FIELDS_INDEX,
        RUN_DATASOURCE_ID, AUDIT_SQL_CLOB, FILE_TYPE, CREATE_TIME,
        UPDATE_TIME, CREATE_BY) values
        <foreach collection="list" item="item" index="index" separator=",">
            ( #{item.ruleName}, #{item.auditFields},
            #{item.auditFieldsIndex},#{item.runDatasourceId}, #{item.auditSqlClob}, #{item.fileType},
            NOW(),NOW(),#{item.createBy})
        </foreach>
    </insert>
    <update id="updateAuditFileDatasourceRule">
        update AUDIT_FILE_DATASOURCE_RULE
        <set>
            <if test="ruleName != null and ruleName != ''">
                RULE_NAME = #{ruleName},
            </if>
            <if test="auditFields != null and auditFields != ''">
                AUDIT_FIELDS = #{auditFields},
            </if>
            <if test="auditFieldsIndex != null and auditFieldsIndex != ''">
                AUDIT_FIELDS_INDEX = #{auditFieldsIndex},
            </if>
            <if test="runDatasourceId != null and runDatasourceId != ''">
                RUN_DATASOURCE_ID = #{runDatasourceId},
            </if>
            <if test="auditSqlClob != null and auditSqlClob != ''">
                AUDIT_SQL_CLOB = #{auditSqlClob},
            </if>
            <if test="fileType != null and fileType != ''">
                FILE_TYPE = #{fileType},
            </if>
            UPDATE_TIME= NOW()
        </set>
        where ID = #{id}
    </update>
    <delete id="deleteAuditFileDatasourceRule">
        delete from AUDIT_FILE_DATASOURCE_RULE where ID in
        <foreach item="id" index="index" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="qryAuditFileDatasourceRuleByCondition"
            resultType="com.suke.czx.newland.po.AuditFileDatasourceRulePo">
        select ID, RULE_NAME, AUDIT_FIELDS, AUDIT_FIELDS_INDEX,
               RUN_DATASOURCE_ID, AUDIT_SQL_CLOB, FILE_TYPE, CREATE_TIME,
               UPDATE_TIME, CREATE_BY
        from AUDIT_FILE_DATASOURCE_RULE where
        1 = 1
        <if test="auditFileRuleName != null and auditFileRuleName != ''">
            and RULE_NAME like concat(concat('%',#{auditFileRuleName}),'%')
        </if>
        <if test="runDatasourceId != null and runDatasourceId != ''">
            and RUN_DATASOURCE_ID = #{runDatasourceId}
        </if>
        <if test="createBy != null and createBy !=''">
            and CREATE_BY like concat(concat('%',#{createBy}),'%')
        </if>
        <if test="searchKey != null and searchKey != ''">
            and RULE_NAME like concat(concat('%',#{searchKey}),'%')
            or CREATE_BY like concat(concat('%',#{searchKey}),'%')
        </if>
        order by CREATE_TIME DESC
    </select>
    <select id="qryAuditFileDatasourceRuleById" resultType="com.suke.czx.newland.po.AuditFileDatasourceRulePo">
        select ID, RULE_NAME, AUDIT_FIELDS, AUDIT_FIELDS_INDEX,
               RUN_DATASOURCE_ID, AUDIT_SQL_CLOB, FILE_TYPE, CREATE_TIME,
               UPDATE_TIME, CREATE_BY
        from AUDIT_FILE_DATASOURCE_RULE where ID = #{id}
    </select>
</mapper>