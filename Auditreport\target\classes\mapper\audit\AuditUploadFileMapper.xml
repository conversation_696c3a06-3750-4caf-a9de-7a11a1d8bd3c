<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suke.czx.newland.dao.mgrdbmapper.AuditUploadFileMapper">
    <insert id="insert">
        insert into AUDIT_UPLOAD_FILE
        (FILE_NAME, FILE_SIZE, MD5, FILE_PATH, UPLOAD_TIME, UPLOAD_USER, FILE_TYPE)
        values (#{fileName}, #{fileSize}, #{md5}, #{filePath}, #{uploadTime},
                #{uploadUser}, #{fileType})
    </insert>
    <insert id="insertReturnId" useGeneratedKeys="false">
        insert into AUDIT_UPLOAD_FILE
        (FILE_NAME, FILE_SIZE, MD5, FILE_PATH, UPLOAD_TIME, UPLOAD_USER, FILE_TYPE)
        values (#{fileName}, #{fileSize}, #{md5}, #{filePath}, #{uploadTime}, #{uploadUser}, #{fileType})
    </insert>
    <delete id="delAuditFileWithAuditFileId">
        update AUDIT_UPLOAD_FILE
        set DELETED = '1'
        where id = ${auditFileId}
    </delete>
    <delete id="delBatchAuditFileWithAuditFileIds">
        update AUDIT_UPLOAD_FILE set DELETED = '1' where id in
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>
    <select id="qryAuditWithCondition" resultType="com.suke.czx.newland.po.AuditUploadFilePo">
        SELECT ID, FILE_NAME, FILE_SIZE, UPLOAD_TIME, FILE_TYPE
        FROM AUDIT_UPLOAD_FILE
        WHERE 1 = 1
        AND DELETED = '0'
        AND FILE_TYPE = '0'
        <if test="fileName != null and fileName != ''">
            AND FILE_NAME LIKE CONCAT('%', #{fileName}, '%')
        </if>
        <if test="uploadBeginTime != null and uploadEndTime != null">
            AND DATE_FORMAT(UPLOAD_TIME, '%Y%m%d%H%i%s') BETWEEN
            DATE_FORMAT(#{uploadBeginTime}, '%Y%m%d%H%i%s') AND
            DATE_FORMAT(#{uploadEndTime}, '%Y%m%d%H%i%s')
        </if>
        ORDER BY ID ASC
    </select>
    <select id="qryAuditFileWithAuditFileId" resultType="com.suke.czx.newland.po.AuditUploadFilePo">
        select ID,
               FILE_NAME,
               MD5,
               FILE_PATH,
               FILE_SIZE,
               UPLOAD_TIME,
               UPLOAD_USER,
               FILE_TYPE
        from AUDIT_UPLOAD_FILE
        where id = ${auditFileId}
          and DELETED = '0'
    </select>
    <select id="qryAuditFileWithAuditFileIdWithoutStatus"
            resultType="com.suke.czx.newland.po.AuditUploadFilePo">
        select ID,
               FILE_NAME,
               MD5,
               FILE_PATH,
               FILE_SIZE,
               UPLOAD_TIME,
               UPLOAD_USER,
               FILE_TYPE
        from AUDIT_UPLOAD_FILE
        where id = ${auditFileId}
    </select>
    <select id="qryAuditFileWithAuditFileIds" resultType="com.suke.czx.newland.po.AuditUploadFilePo">
        select ID,FILE_NAME,FILE_SIZE,FILE_PATH,UPLOAD_TIME,FILE_TYPE from AUDIT_UPLOAD_FILE
        where 1 = 1 and DELETED = '0' and id in
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>