<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suke.czx.modules.oss.dao.SysOssDao">

	<select id="queryObject" resultType="com.suke.czx.modules.oss.entity.SysOssEntity">
		select * from sys_oss where id = #{value}
	</select>

	<select id="queryList" resultType="com.suke.czx.modules.oss.entity.SysOssEntity">
		select * from sys_oss order by id desc
		<if test="offset != null and limit != null">
			limit #{offset}, #{limit}
		</if>
	</select>
	
 	<select id="queryTotal" resultType="int">
		select count(*) from sys_oss 
	</select>
	 
	<insert id="save" parameterType="com.suke.czx.modules.oss.entity.SysOssEntity" useGeneratedKeys="true" keyProperty="id">
		insert into sys_oss
		(
			`url`, 
			`create_date`
		)
		values
		(
			#{url}, 
			#{createDate}
		)
	</insert>
	 
	<update id="update" parameterType="com.suke.czx.modules.oss.entity.SysOssEntity">
		update sys_oss 
		<set>
			<if test="url != null">`url` = #{url}, </if>
			<if test="createDate != null">`create_date` = #{createDate}</if>
		</set>
		where id = #{id}
	</update>
	
	<delete id="delete">
		delete from sys_oss where id = #{value}
	</delete>
	
	<delete id="deleteBatch">
		delete from sys_oss where id in 
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>

</mapper>