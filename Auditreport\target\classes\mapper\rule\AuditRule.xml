<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.suke.czx.modules.rule.dao.AuditRuleDao">

    <insert id="saveAuditRule">
        insert into audit_rule(id,name,create_user_id) values(#{id},#{name},#{createUserId})
    </insert>
    <insert id="add">
        insert into audit_rule (ID, NAME, CREATE_USER_ID, DELETED, CREATE_TIME,TITLE_ROW)
        values (#{id},#{name},#{createUserId},0,NOW(),#{titleRow})
    </insert>
    <update id="updateRuleById">
        update audit_rule set name = #{name},update_user_id = #{updateUserId},update_time = #{updateTime} where id = #{id}
    </update>

    <select id="queryTotal" resultType="int">
        select count(*) from audit_rule where name = #{id}
    </select>

    <!-- 查询全部规则父表 -->
    <select id="queryAllRule" resultType="com.suke.czx.modules.rule.entity.AuditRule">
        select * from audit_rule
        <where>
            <if test="id != '' and id != null">
                 id = #{id}
            </if>
        </where>
        order by CREATE_TIME desc
    </select>
    <select id="queryAuditRule" resultType="com.suke.czx.modules.rule.entity.AuditRule">
        select * from audit_rule where name = #{name}
    </select>
    <select id="queryAuditRuleUrl" resultType="java.util.Map">
        select * from  Audit_Rule_Url a where a.url_id = #{url_id}
    </select>
    <select id="queryAuditRuleWithParam" resultType="com.suke.czx.modules.rule.entity.AuditRule">
        select ID,NAME,CREATE_USER_ID,DELETED,CREATE_TIME,UPDATE_USER_ID,UPDATE_TIME,TITLE_ROW from audit_rule where
            NAME like concat(concat('%',#{keyword}),'%') order by CREATE_TIME desc
    </select>
    <delete id="delete" parameterType="string">
        delete from audit_rule where id = #{id}
    </delete>
</mapper>