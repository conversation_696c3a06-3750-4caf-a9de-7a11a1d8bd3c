<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suke.czx.modules.sys.dao.SysMenuDao">

	<select id="queryObject" resultType="com.suke.czx.modules.sys.entity.SysMenuEntity">
		select * from audit_menu where id = #{value}
	</select>
	
	<insert id="save" parameterType="com.suke.czx.modules.sys.entity.SysMenuEntity">
		insert into audit_menu
		(
			parent_id,
			name,
			url,
		 	component,
			perms,
			type,
			icon,
			order_num
		)
		values
		(
			#{parentId}, 
			#{name}, 
			#{url}, 
			#{component},
			#{perms,jdbcType=VARCHAR},
			#{type}, 
			#{icon}, 
			#{orderNum}
		)
	</insert>
	
	<select id="queryListParentId" resultType="com.suke.czx.modules.sys.entity.SysMenuEntity">
		select * from audit_menu where parent_id = #{parentId} order by order_num asc
	</select>
	
	<select id="queryNotButtonList" resultType="com.suke.czx.modules.sys.entity.SysMenuEntity">
		select * from audit_menu where type != "2" order by order_num asc
	</select>
	
	<select id="queryList" resultType="com.suke.czx.modules.sys.entity.SysMenuEntity">
		select m.*,(select p.name from audit_menu p where p.id = m.parent_id) as parentName
			from audit_menu m
		order by m.order_num asc
		<if test="offset != null and limit != null">
			limit #{offset}, #{limit}
		</if>
	</select>
	
	<!-- 查询用户的权限列表 --> 
	<select id="queryUserList" resultType="com.suke.czx.modules.sys.entity.SysMenuEntity">
		select distinct m.*,(select p.name from audit_menu p where p.id = m.parent_id) as parentName
		from audit_user_role ur
				 inner JOIN audit_role_menu rm on ur.role_id = rm.role_id
				 inner JOIN audit_menu m on rm.menu_id = m.id
		where ur.user_id = #{userId} order by m.order_num asc
	</select>
	
	<select id="queryTotal" resultType="int">
		select count(*) from audit_menu
	</select>

	<select id="queryMenuParentId" resultType="java.lang.Long">
		select parent_id from audit_menu where id = #{id}
	</select>

	<update id="update" parameterType="com.suke.czx.modules.sys.entity.SysMenuEntity">
		update audit_menu
		<set>
			<if test="parentId != null">parent_id = #{parentId}, </if>
			<if test="name != null">name = #{name}, </if>
			<if test="url != null">url = #{url}, </if>
			<if test="perms != null">perms = #{perms}, </if>
			<if test="type != null">type = #{type}, </if>
			<if test="icon != null">icon = #{icon}, </if>
			<if test="orderNum != null">order_num = #{orderNum},</if>
			<if test="component != null">component = #{component}</if>
		</set>
		where id = #{id}
	</update>

	<delete id="delete">
		delete from audit_menu where id = #{id}
	</delete>
	
	<delete id="deleteBatch">
		delete from audit_menu where id in
		<foreach item="menuId" collection="array" open="(" separator="," close=")">
			#{menuId}
		</foreach>
		;
		delete from audit_role_menu where menu_id in
		<foreach item="menuId" collection="array" open="(" separator="," close=")">
			#{menuId}
		</foreach>
	</delete>
	
</mapper>