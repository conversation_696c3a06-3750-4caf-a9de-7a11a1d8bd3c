<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suke.czx.modules.sys.dao.SysRoleDao">

	<select id="queryObject" resultType="com.suke.czx.modules.sys.entity.SysRoleEntity">
		select * from audit_role where id = #{value}
	</select>
	
	<select id="queryList" resultType="com.suke.czx.modules.sys.entity.SysRoleEntity">
		select * from audit_role
		<where>
			<if test="roleName != null and roleName.trim() != ''">
				and role_name like concat('%',#{roleName},'%')
			</if>
			<!-- <if test="createUserId != null"> -->
			<!-- 	and create_user_id = #{createUserId}  -->
			<!-- </if> -->
		</where>
		order by id asc
		<if test="offset != null and limit != null">
			limit #{offset}, #{limit}
		</if>
	</select>
	
	<select id="queryTotal" resultType="int">
		select count(*) from audit_role
		<where>
			<if test="roleName != null and roleName.trim() != ''">
				and role_name like concat('%',#{roleName},'%')
			</if>
			<if test="createUserId != null">
				and create_user_id = #{createUserId} 
			</if>
		</where>
	</select>
	
	<insert id="save" parameterType="com.suke.czx.modules.sys.entity.SysRoleEntity">
		insert into audit_role
		(
			role_name,
			remark,
			create_user_id
		)
		values
		(
			#{roleName}, 
			#{remark},
			#{createUserId}
		)
	</insert>
	 
	<update id="update" parameterType="com.suke.czx.modules.sys.entity.SysRoleEntity">
		update audit_role
		<set>
			<if test="roleName != null">role_name = #{roleName}, </if>
			<if test="remark != null">remark = #{remark}</if>
		</set>
		where id = #{id}
	</update>
	
	<delete id="deleteBatch">
		delete from audit_role where id in
		<foreach item="roleId" collection="array" open="(" separator="," close=")">
			#{roleId}
		</foreach>
		<!-- ; -->
		<!-- delete from audit_role_menu where role_id in -->
		<!-- <foreach item="roleId" collection="array" open="(" separator="," close=")"> -->
		<!-- 	#{roleId} -->
		<!-- </foreach> -->
		<!-- ; -->
		<!-- delete from audit_user_role where role_id in -->
		<!-- <foreach item="roleId" collection="array" open="(" separator="," close=")"> -->
		<!-- 	#{roleId} -->
		<!-- </foreach> -->
	</delete>
	
	<!-- 查询用户创建的角色ID列表 -->
	<select id="queryRoleIdList" resultType="long">
		select id from audit_role where create_user_id = #{createUserId}
	</select>
	<select id="queryTotalByRoleName" resultType="java.lang.Integer">
		select count(*) from audit_role where role_name = #{roleName}
	</select>
	<select id="qryRoleNameByRoleIds" resultType="java.lang.String">
		select ROLE_NAME from AUDIT_ROLE where 1 = 1
		and ID in
		<foreach item="roleId" collection="collection" open="(" separator="," close=")">
			#{roleId}
		</foreach>
	</select>
</mapper>