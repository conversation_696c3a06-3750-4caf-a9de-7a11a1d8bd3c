<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suke.czx.modules.sys.dao.SysUserRoleDao">

	<select id="queryList" resultType="com.suke.czx.modules.sys.entity.SysUserRoleEntity">
		select * from sys_user_role 
		<where>
			<if test="userId != null">
				user_id = #{userId}
			</if>
		</where>
	</select>
 
	<insert id="save">
		insert into sys_user_role
		(
			`user_id`, 
			`role_id`
		)
		values
		<foreach collection="roleIdList" item="item" index="index" separator="," >
		(
			#{userId}, 
			#{item}
		)
		</foreach>
	</insert>
	<insert id="saveUserRole">
		insert into audit_user_role(user_id,role_id) values (#{userId},#{item})
	</insert>

	<delete id="delete">
		delete from audit_user_role where user_id = #{value}
	</delete>
	
	<select id="queryRoleIdList" resultType="long">
		select role_id from audit_user_role where user_id = #{value}
	</select>

	<select id="queryTotal" resultType="int">
		select count(*) from audit_user_role where role_id = #{id}
	</select>
</mapper>