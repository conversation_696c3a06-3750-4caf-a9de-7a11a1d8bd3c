<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suke.czx.modules.sys.dao.SysUserTokenDao">

	<select id="queryByUserId" resultType="com.suke.czx.modules.sys.entity.SysUserTokenEntity">
		select * from audit_user_token where user_id = #{value}
	</select>

	<select id="queryByToken" resultType="com.suke.czx.modules.sys.entity.SysUserTokenEntity">
		select * from audit_user_token where token = #{value}
	</select>
	 
	<insert id="save" parameterType="com.suke.czx.modules.sys.entity.SysUserTokenEntity">
		insert into audit_user_token
		(
			user_id,
			token,
			expire_time,
			update_time
		)
		values
		(
			#{userId}, 
			#{token}, 
			#{expireTime},
			#{updateTime}
		)
	</insert>
	<insert id="saveToken">
		insert into audit_user_token(user_id,token,expire_time,update_time) values (#{userId},#{token},#{expireTime},#{updateTime})
	</insert>

	<update id="update" parameterType="com.suke.czx.modules.sys.entity.SysUserTokenEntity">
		update audit_user_token
		<set>
			<if test="token != null">token = #{token}, </if>
			<if test="expireTime != null">expire_time = #{expireTime}, </if>
			<if test="updateTime != null">update_time = #{updateTime}</if>
		</set>
		where user_id = #{userId}
	</update>

</mapper>