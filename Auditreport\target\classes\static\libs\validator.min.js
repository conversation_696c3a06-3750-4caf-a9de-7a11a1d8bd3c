/*!
 * Copyright (c) 2016 <PERSON> <<EMAIL>>
 * 
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 * 
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.validator=e()}(this,function(){"use strict";function t(t){if(!("string"==typeof t||t instanceof String))throw new TypeError("This library (validator.js) validates strings only")}function e(e){return t(e),e=Date.parse(e),isNaN(e)?null:new Date(e)}function r(e){return t(e),parseFloat(e)}function o(t){return"object"===(void 0===t?"undefined":v(t))&&null!==t?t="function"==typeof t.toString?t.toString():"[object Object]":(null===t||void 0===t||isNaN(t)&&!t.length)&&(t=""),String(t)}function i(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments[1];for(var r in e)void 0===t[r]&&(t[r]=e[r]);return t}function n(e,r){t(e);var o=void 0,i=void 0;"object"===(void 0===r?"undefined":v(r))?(o=r.min||0,i=r.max):(o=arguments[1],i=arguments[2]);var n=encodeURI(e).split(/%..|./).length-1;return n>=o&&(void 0===i||n<=i)}function l(e,r){t(e),(r=i(r,F)).allow_trailing_dot&&"."===e[e.length-1]&&(e=e.substring(0,e.length-1));var o=e.split(".");if(r.require_tld){var n=o.pop();if(!o.length||!/^([a-z\u00a1-\uffff]{2,}|xn[a-z0-9-]{2,})$/i.test(n))return!1;if(/[\s\u2002-\u200B\u202F\u205F\u3000\uFEFF\uDB40\uDC20]/.test(n))return!1}for(var l,a=0;a<o.length;a++){if(l=o[a],r.allow_underscores&&(l=l.replace(/_/g,"")),!/^[a-z\u00a1-\uffff0-9-]+$/i.test(l))return!1;if(/[\uff01-\uff5e]/.test(l))return!1;if("-"===l[0]||"-"===l[l.length-1])return!1}return!0}function a(e,r){if(t(e),(r=i(r,$)).require_display_name||r.allow_display_name){var o=e.match(x);if(o)e=o[1];else if(r.require_display_name)return!1}var a=e.split("@"),u=a.pop(),s=a.join("@"),d=u.toLowerCase();if("gmail.com"!==d&&"googlemail.com"!==d||(s=s.replace(/\./g,"").toLowerCase()),!n(s,{max:64})||!n(u,{max:254}))return!1;if(!l(u,{require_tld:r.require_tld}))return!1;if('"'===s[0])return s=s.slice(1,s.length-1),r.allow_utf8_local_part?b.test(s):w.test(s);for(var c=r.allow_utf8_local_part?y:A,f=s.split("."),p=0;p<f.length;p++)if(!c.test(f[p]))return!1;return!0}function u(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(t(e),!(r=String(r)))return u(e,4)||u(e,6);if("4"===r)return!!k.test(e)&&e.split(".").sort(function(t,e){return t-e})[3]<=255;if("6"===r){var o=e.split(":"),i=!1,n=u(o[o.length-1],4),l=n?7:8;if(o.length>l)return!1;if("::"===e)return!0;"::"===e.substr(0,2)?(o.shift(),o.shift(),i=!0):"::"===e.substr(e.length-2)&&(o.pop(),o.pop(),i=!0);for(var a=0;a<o.length;++a)if(""===o[a]&&a>0&&a<o.length-1){if(i)return!1;i=!0}else if(n&&a===o.length-1);else if(!D.test(o[a]))return!1;return i?o.length>=1:o.length===l}return!1}function s(t){return"[object RegExp]"===Object.prototype.toString.call(t)}function d(t,e){for(var r=0;r<e.length;r++){var o=e[r];if(t===o||s(o)&&o.test(t))return!0}return!1}function c(e){return t(e),G.test(e)}function f(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(t(e),!(r=String(r)))return f(e,10)||f(e,13);var o=e.replace(/[\s-]+/g,""),i=0,n=void 0;if("10"===r){if(!et.test(o))return!1;for(n=0;n<9;n++)i+=(n+1)*o.charAt(n);if("X"===o.charAt(9)?i+=100:i+=10*o.charAt(9),i%11==0)return!!o}else if("13"===r){if(!rt.test(o))return!1;for(n=0;n<12;n++)i+=ot[n%2]*o.charAt(n);if(o.charAt(12)-(10-i%10)%10==0)return!!o}return!1}function p(t){var e="(\\"+t.symbol.replace(/\./g,"\\.")+")"+(t.require_symbol?"":"?"),r="("+["0","[1-9]\\d*","[1-9]\\d{0,2}(\\"+t.thousands_separator+"\\d{3})*"].join("|")+")?"+("(\\"+t.decimal_separator+"\\d{2})?");return t.allow_negatives&&!t.parens_for_negatives&&(t.negative_sign_after_digits?r+="-?":t.negative_sign_before_digits&&(r="-?"+r)),t.allow_negative_sign_placeholder?r="( (?!\\-))?"+r:t.allow_space_after_symbol?r=" ?"+r:t.allow_space_after_digits&&(r+="( (?!$))?"),t.symbol_after_digits?r+=e:r=e+r,t.allow_negatives&&(t.parens_for_negatives?r="(\\("+r+"\\)|"+r+")":t.negative_sign_before_digits||t.negative_sign_after_digits||(r="-?"+r)),new RegExp("^(?!-? )(?=.*\\d)"+r+"$")}function g(e,r){t(e);var o=r?new RegExp("^["+r+"]+","g"):/^\s+/g;return e.replace(o,"")}function h(e,r){t(e);for(var o=r?new RegExp("["+r+"]"):/\s/,i=e.length-1;i>=0&&o.test(e[i]);)i--;return i<e.length?e.substr(0,i+1):e}function m(e,r){return t(e),e.replace(new RegExp("["+r+"]+","g"),"")}for(var _,v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},F={require_tld:!0,allow_underscores:!1,allow_trailing_dot:!1},$={allow_display_name:!1,require_display_name:!1,allow_utf8_local_part:!0,require_tld:!0},x=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~\.\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~\.\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF\s]*<(.+)>$/i,A=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~]+$/i,w=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f]))*$/i,y=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+$/i,b=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))*$/i,k=/^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/,D=/^[0-9A-F]{1,4}$/i,S={protocols:["http","https","ftp"],require_tld:!0,require_protocol:!1,require_host:!0,require_valid_protocol:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_protocol_relative_urls:!1},E=/^\[([^\]]+)\](?::([0-9]+))?$/,Z=/^([0-9a-fA-F][0-9a-fA-F]:){5}([0-9a-fA-F][0-9a-fA-F])$/,O={"en-US":/^[A-Z]+$/i,"cs-CZ":/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]+$/i,"da-DK":/^[A-ZÆØÅ]+$/i,"de-DE":/^[A-ZÄÖÜß]+$/i,"es-ES":/^[A-ZÁÉÍÑÓÚÜ]+$/i,"fr-FR":/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]+$/i,"nl-NL":/^[A-ZÉËÏÓÖÜ]+$/i,"hu-HU":/^[A-ZÁÉÍÓÖŐÚÜŰ]+$/i,"pl-PL":/^[A-ZĄĆĘŚŁŃÓŻŹ]+$/i,"pt-PT":/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]+$/i,"ru-RU":/^[А-ЯЁ]+$/i,"sr-RS@latin":/^[A-ZČĆŽŠĐ]+$/i,"sr-RS":/^[А-ЯЂЈЉЊЋЏ]+$/i,"tr-TR":/^[A-ZÇĞİıÖŞÜ]+$/i,"uk-UA":/^[А-ЩЬЮЯЄIЇҐ]+$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]+$/},R={"en-US":/^[0-9A-Z]+$/i,"cs-CZ":/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]+$/i,"da-DK":/^[0-9A-ZÆØÅ]$/i,"de-DE":/^[0-9A-ZÄÖÜß]+$/i,"es-ES":/^[0-9A-ZÁÉÍÑÓÚÜ]+$/i,"fr-FR":/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]+$/i,"hu-HU":/^[0-9A-ZÁÉÍÓÖŐÚÜŰ]+$/i,"nl-NL":/^[0-9A-ZÉËÏÓÖÜ]+$/i,"pl-PL":/^[0-9A-ZĄĆĘŚŁŃÓŻŹ]+$/i,"pt-PT":/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]+$/i,"ru-RU":/^[0-9А-ЯЁ]+$/i,"sr-RS@latin":/^[0-9A-ZČĆŽŠĐ]+$/i,"sr-RS":/^[0-9А-ЯЂЈЉЊЋЏ]+$/i,"tr-TR":/^[0-9A-ZÇĞİıÖŞÜ]+$/i,"uk-UA":/^[0-9А-ЩЬЮЯЄIЇҐ]+$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]+$/},C=["AU","GB","HK","IN","NZ","ZA","ZM"],I=0;I<C.length;I++)O[_="en-"+C[I]]=O["en-US"],R[_]=R["en-US"];O["pt-BR"]=O["pt-PT"],R["pt-BR"]=R["pt-PT"];for(var j,U=["AE","BH","DZ","EG","IQ","JO","KW","LB","LY","MA","QM","QA","SA","SD","SY","TN","YE"],z=0;z<U.length;z++)O[j="ar-"+U[z]]=O.ar,R[j]=R.ar;var B=/^[-+]?[0-9]+$/,P=/^[\x00-\x7F]+$/,L=/[^\u0020-\u007E\uFF61-\uFF9F\uFFA0-\uFFDC\uFFE8-\uFFEE0-9a-zA-Z]/,N=/[\u0020-\u007E\uFF61-\uFF9F\uFFA0-\uFFDC\uFFE8-\uFFEE0-9a-zA-Z]/,q=/[^\x00-\x7F]/,T=/[\uD800-\uDBFF][\uDC00-\uDFFF]/,H=/^(?:[-+]?(?:0|[1-9][0-9]*))$/,K=/^[-+]?[0-9]+$/,M=/^(?:[-+])?(?:[0-9]+)?(?:\.[0-9]*)?(?:[eE][\+\-]?(?:[0-9]+))?$/,W=/^[-+]?([0-9]+|\.[0-9]+|[0-9]+\.[0-9]+)$/,G=/^[0-9A-F]+$/i,Y=/^#?([0-9A-F]{3}|[0-9A-F]{6})$/i,J=/^[A-Z]{2}[0-9A-Z]{3}\d{2}\d{5}$/,Q=/^[a-f0-9]{32}$/,X={3:/^[0-9A-F]{8}-[0-9A-F]{4}-3[0-9A-F]{3}-[0-9A-F]{4}-[0-9A-F]{12}$/i,4:/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,5:/^[0-9A-F]{8}-[0-9A-F]{4}-5[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,all:/^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/i},V=/^(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|(222[1-9]|22[3-9][0-9]|2[3-6][0-9]{2}|27[01][0-9]|2720)[0-9]{12}|6(?:011|5[0-9][0-9])[0-9]{12}|3[47][0-9]{13}|3(?:0[0-5]|[68][0-9])[0-9]{11}|(?:2131|1800|35\d{3})\d{11}|62[0-9]{14})$/,tt=/^[A-Z]{2}[0-9A-Z]{9}[0-9]$/,et=/^(?:[0-9]{9}X|[0-9]{10})$/,rt=/^(?:[0-9]{13})$/,ot=[1,3],it="^\\d{4}-?\\d{3}[\\dX]$",nt={"ar-DZ":/^(\+?213|0)(5|6|7)\d{8}$/,"ar-SY":/^(!?(\+?963)|0)?9\d{8}$/,"ar-SA":/^(!?(\+?966)|0)?5\d{8}$/,"en-US":/^(\+?1)?[2-9]\d{2}[2-9](?!11)\d{6}$/,"cs-CZ":/^(\+?420)? ?[1-9][0-9]{2} ?[0-9]{3} ?[0-9]{3}$/,"de-DE":/^(\+?49[ \.\-])?([\(]{1}[0-9]{1,6}[\)])?([0-9 \.\-\/]{3,20})((x|ext|extension)[ ]?[0-9]{1,4})?$/,"da-DK":/^(\+?45)?(\d{8})$/,"el-GR":/^(\+?30)?(69\d{8})$/,"en-AU":/^(\+?61|0)4\d{8}$/,"en-GB":/^(\+?44|0)7\d{9}$/,"en-HK":/^(\+?852\-?)?[569]\d{3}\-?\d{4}$/,"en-IN":/^(\+?91|0)?[789]\d{9}$/,"en-KE":/^(\+?254|0)?[7]\d{8}$/,"en-NG":/^(\+?234|0)?[789]\d{9}$/,"en-NZ":/^(\+?64|0)2\d{7,9}$/,"en-UG":/^(\+?256|0)?[7]\d{8}$/,"en-RW":/^(\+?250|0)?[7]\d{8}$/,"en-TZ":/^(\+?255|0)?[67]\d{8}$/,"en-ZA":/^(\+?27|0)\d{9}$/,"en-ZM":/^(\+?26)?09[567]\d{7}$/,"es-ES":/^(\+?34)?(6\d{1}|7[1234])\d{7}$/,"fi-FI":/^(\+?358|0)\s?(4(0|1|2|4|5|6)?|50)\s?(\d\s?){4,8}\d$/,"fa-IR":/^(\+?98[\-\s]?|0)9[0-39]\d[\-\s]?\d{3}[\-\s]?\d{4}$/,"fr-FR":/^(\+?33|0)[67]\d{8}$/,"he-IL":/^(\+972|0)([23489]|5[0248]|77)[1-9]\d{6}/,"hu-HU":/^(\+?36)(20|30|70)\d{7}$/,"lt-LT":/^(\+370|8)\d{8}$/,"id-ID":/^(\+?62|0[1-9])[\s|\d]+$/,"it-IT":/^(\+?39)?\s?3\d{2} ?\d{6,7}$/,"ko-KR":/^((\+?82)[ \-]?)?0?1([0|1|6|7|8|9]{1})[ \-]?\d{3,4}[ \-]?\d{4}$/,"ja-JP":/^(\+?81|0)\d{1,4}[ \-]?\d{1,4}[ \-]?\d{4}$/,"ms-MY":/^(\+?6?01){1}(([145]{1}(\-|\s)?\d{7,8})|([236789]{1}(\s|\-)?\d{7}))$/,"nb-NO":/^(\+?47)?[49]\d{7}$/,"nl-BE":/^(\+?32|0)4?\d{8}$/,"nn-NO":/^(\+?47)?[49]\d{7}$/,"pl-PL":/^(\+?48)? ?[5-8]\d ?\d{3} ?\d{2} ?\d{2}$/,"pt-BR":/^(\+?55|0)\-?[1-9]{2}\-?[2-9]{1}\d{3,4}\-?\d{4}$/,"pt-PT":/^(\+?351)?9[1236]\d{7}$/,"ro-RO":/^(\+?4?0)\s?7\d{2}(\/|\s|\.|\-)?\d{3}(\s|\.|\-)?\d{3}$/,"en-PK":/^((\+92)|(0092))-{0,1}\d{3}-{0,1}\d{7}$|^\d{11}$|^\d{4}-\d{7}$/,"ru-RU":/^(\+?7|8)?9\d{9}$/,"sr-RS":/^(\+3816|06)[- \d]{5,9}$/,"tr-TR":/^(\+?90|0)?5\d{9}$/,"vi-VN":/^(\+?84|0)?((1(2([0-9])|6([2-9])|88|99))|(9((?!5)[0-9])))([0-9]{7})$/,"zh-CN":/^(\+?0?86\-?)?1[345789]\d{9}$/,"zh-TW":/^(\+?886\-?|0)?9\d{8}$/};nt["en-CA"]=nt["en-US"],nt["fr-BE"]=nt["nl-BE"],nt["zh-HK"]=nt["en-HK"];var lt={symbol:"$",require_symbol:!1,allow_space_after_symbol:!1,symbol_after_digits:!1,allow_negatives:!0,parens_for_negatives:!1,negative_sign_before_digits:!1,negative_sign_after_digits:!1,allow_negative_sign_placeholder:!1,thousands_separator:",",decimal_separator:".",allow_space_after_digits:!1},at=/^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-2])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T\s]((([01]\d|2[0-3])((:?)[0-5]\d)?|24:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/,ut=/[^A-Z0-9+\/=]/i,st=/^\s*data:([a-z]+\/[a-z0-9\-\+]+(;[a-z\-]+=[a-z0-9\-]+)?)?(;base64)?,[a-z0-9!\$&',\(\)\*\+,;=\-\._~:@\/\?%\s]*\s*$/i,dt={all_lowercase:!0,gmail_lowercase:!0,gmail_remove_dots:!0,gmail_remove_subaddress:!0,gmail_convert_googlemaildotcom:!0,outlookdotcom_lowercase:!0,outlookdotcom_remove_subaddress:!0,yahoo_lowercase:!0,yahoo_remove_subaddress:!0,icloud_lowercase:!0,icloud_remove_subaddress:!0},ct=["icloud.com","me.com"],ft=["hotmail.at","hotmail.be","hotmail.ca","hotmail.cl","hotmail.co.il","hotmail.co.nz","hotmail.co.th","hotmail.co.uk","hotmail.com","hotmail.com.ar","hotmail.com.au","hotmail.com.br","hotmail.com.gr","hotmail.com.mx","hotmail.com.pe","hotmail.com.tr","hotmail.com.vn","hotmail.cz","hotmail.de","hotmail.dk","hotmail.es","hotmail.fr","hotmail.hu","hotmail.id","hotmail.ie","hotmail.in","hotmail.it","hotmail.jp","hotmail.kr","hotmail.lv","hotmail.my","hotmail.ph","hotmail.pt","hotmail.sa","hotmail.sg","hotmail.sk","live.be","live.co.uk","live.com","live.com.ar","live.com.mx","live.de","live.es","live.eu","live.fr","live.it","live.nl","msn.com","outlook.at","outlook.be","outlook.cl","outlook.co.il","outlook.co.nz","outlook.co.th","outlook.com","outlook.com.ar","outlook.com.au","outlook.com.br","outlook.com.gr","outlook.com.pe","outlook.com.tr","outlook.com.vn","outlook.cz","outlook.de","outlook.dk","outlook.es","outlook.fr","outlook.hu","outlook.id","outlook.ie","outlook.in","outlook.it","outlook.jp","outlook.kr","outlook.lv","outlook.my","outlook.ph","outlook.pt","outlook.sa","outlook.sg","outlook.sk","passport.com"],pt=["rocketmail.com","yahoo.ca","yahoo.co.uk","yahoo.com","yahoo.de","yahoo.fr","yahoo.in","yahoo.it","ymail.com"];return{version:"8.0.0",toDate:e,toFloat:r,toInt:function(e,r){return t(e),parseInt(e,r||10)},toBoolean:function(e,r){return t(e),r?"1"===e||"true"===e:"0"!==e&&"false"!==e&&""!==e},equals:function(e,r){return t(e),e===r},contains:function(e,r){return t(e),e.indexOf(o(r))>=0},matches:function(e,r,o){return t(e),"[object RegExp]"!==Object.prototype.toString.call(r)&&(r=new RegExp(r,o)),r.test(e)},isEmail:a,isURL:function(e,r){if(t(e),!e||e.length>=2083||/[\s<>]/.test(e))return!1;if(0===e.indexOf("mailto:"))return!1;r=i(r,S);var o=void 0,n=void 0,a=void 0,s=void 0,c=void 0,f=void 0,p=void 0,g=void 0;if(p=e.split("#"),e=p.shift(),p=e.split("?"),e=p.shift(),(p=e.split("://")).length>1){if(o=p.shift(),r.require_valid_protocol&&-1===r.protocols.indexOf(o))return!1}else{if(r.require_protocol)return!1;r.allow_protocol_relative_urls&&"//"===e.substr(0,2)&&(p[0]=e.substr(2))}if(""===(e=p.join("://")))return!1;if(p=e.split("/"),""===(e=p.shift())&&!r.require_host)return!0;if((p=e.split("@")).length>1&&(n=p.shift()).indexOf(":")>=0&&n.split(":").length>2)return!1;f=null,g=null;var h=(s=p.join("@")).match(E);return h?(a="",g=h[1],f=h[2]||null):(a=(p=s.split(":")).shift(),p.length&&(f=p.join(":"))),!(null!==f&&(c=parseInt(f,10),!/^[0-9]+$/.test(f)||c<=0||c>65535)||!(u(a)||l(a,r)||g&&u(g,6))||(a=a||g,r.host_whitelist&&!d(a,r.host_whitelist)||r.host_blacklist&&d(a,r.host_blacklist)))},isMACAddress:function(e){return t(e),Z.test(e)},isIP:u,isFQDN:l,isBoolean:function(e){return t(e),["true","false","1","0"].indexOf(e)>=0},isAlpha:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US";if(t(e),r in O)return O[r].test(e);throw new Error("Invalid locale '"+r+"'")},isAlphanumeric:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US";if(t(e),r in R)return R[r].test(e);throw new Error("Invalid locale '"+r+"'")},isNumeric:function(e){return t(e),B.test(e)},isLowercase:function(e){return t(e),e===e.toLowerCase()},isUppercase:function(e){return t(e),e===e.toUpperCase()},isAscii:function(e){return t(e),P.test(e)},isFullWidth:function(e){return t(e),L.test(e)},isHalfWidth:function(e){return t(e),N.test(e)},isVariableWidth:function(e){return t(e),L.test(e)&&N.test(e)},isMultibyte:function(e){return t(e),q.test(e)},isSurrogatePair:function(e){return t(e),T.test(e)},isInt:function(e,r){t(e);var o=(r=r||{}).hasOwnProperty("allow_leading_zeroes")&&!r.allow_leading_zeroes?H:K,i=!r.hasOwnProperty("min")||e>=r.min,n=!r.hasOwnProperty("max")||e<=r.max,l=!r.hasOwnProperty("lt")||e<r.lt,a=!r.hasOwnProperty("gt")||e>r.gt;return o.test(e)&&i&&n&&l&&a},isFloat:function(e,r){return t(e),r=r||{},""!==e&&"."!==e&&M.test(e)&&(!r.hasOwnProperty("min")||e>=r.min)&&(!r.hasOwnProperty("max")||e<=r.max)&&(!r.hasOwnProperty("lt")||e<r.lt)&&(!r.hasOwnProperty("gt")||e>r.gt)},isDecimal:function(e){return t(e),""!==e&&W.test(e)},isHexadecimal:c,isDivisibleBy:function(e,o){return t(e),r(e)%parseInt(o,10)==0},isHexColor:function(e){return t(e),Y.test(e)},isISRC:function(e){return t(e),J.test(e)},isMD5:function(e){return t(e),Q.test(e)},isJSON:function(e){t(e);try{var r=JSON.parse(e);return!!r&&"object"===(void 0===r?"undefined":v(r))}catch(t){}return!1},isEmpty:function(e){return t(e),0===e.length},isLength:function(e,r){t(e);var o=void 0,i=void 0;"object"===(void 0===r?"undefined":v(r))?(o=r.min||0,i=r.max):(o=arguments[1],i=arguments[2]);var n=e.match(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g)||[],l=e.length-n.length;return l>=o&&(void 0===i||l<=i)},isByteLength:n,isUUID:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"all";t(e);var o=X[r];return o&&o.test(e)},isMongoId:function(e){return t(e),c(e)&&24===e.length},isAfter:function(r){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:String(new Date);t(r);var i=e(o),n=e(r);return!!(n&&i&&n>i)},isBefore:function(r){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:String(new Date);t(r);var i=e(o),n=e(r);return!!(n&&i&&n<i)},isIn:function(e,r){t(e);var i=void 0;if("[object Array]"===Object.prototype.toString.call(r)){var n=[];for(i in r)({}).hasOwnProperty.call(r,i)&&(n[i]=o(r[i]));return n.indexOf(e)>=0}return"object"===(void 0===r?"undefined":v(r))?r.hasOwnProperty(e):!(!r||"function"!=typeof r.indexOf)&&r.indexOf(e)>=0},isCreditCard:function(e){t(e);var r=e.replace(/[- ]+/g,"");if(!V.test(r))return!1;for(var o=0,i=void 0,n=void 0,l=void 0,a=r.length-1;a>=0;a--)i=r.substring(a,a+1),n=parseInt(i,10),o+=l&&(n*=2)>=10?n%10+1:n,l=!l;return!(o%10!=0||!r)},isISIN:function(e){if(t(e),!tt.test(e))return!1;for(var r=e.replace(/[A-Z]/g,function(t){return parseInt(t,36)}),o=0,i=void 0,n=void 0,l=!0,a=r.length-2;a>=0;a--)i=r.substring(a,a+1),n=parseInt(i,10),o+=l&&(n*=2)>=10?n+1:n,l=!l;return parseInt(e.substr(e.length-1),10)===(1e4-o)%10},isISBN:f,isISSN:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t(e);var o=it;if(o=r.require_hyphen?o.replace("?",""):o,!(o=r.case_sensitive?new RegExp(o):new RegExp(o,"i")).test(e))return!1;var i=e.replace("-",""),n=8,l=0,a=!0,u=!1,s=void 0;try{for(var d,c=i[Symbol.iterator]();!(a=(d=c.next()).done);a=!0){var f=d.value;l+=("X"===f.toUpperCase()?10:+f)*n,--n}}catch(t){u=!0,s=t}finally{try{!a&&c.return&&c.return()}finally{if(u)throw s}}return l%11==0},isMobilePhone:function(e,r){if(t(e),r in nt)return nt[r].test(e);if("any"===r)return!!Object.values(nt).find(function(t){return t.test(e)});throw new Error("Invalid locale '"+r+"'")},isCurrency:function(e,r){return t(e),r=i(r,lt),p(r).test(e)},isISO8601:function(e){return t(e),at.test(e)},isBase64:function(e){t(e);var r=e.length;if(!r||r%4!=0||ut.test(e))return!1;var o=e.indexOf("=");return-1===o||o===r-1||o===r-2&&"="===e[r-1]},isDataURI:function(e){return t(e),st.test(e)},ltrim:g,rtrim:h,trim:function(t,e){return h(g(t,e),e)},escape:function(e){return t(e),e.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\//g,"&#x2F;").replace(/\\/g,"&#x5C;").replace(/`/g,"&#96;")},unescape:function(e){return t(e),e.replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#x27;/g,"'").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&#x2F;/g,"/").replace(/&#96;/g,"`")},stripLow:function(e,r){return t(e),m(e,r?"\\x00-\\x09\\x0B\\x0C\\x0E-\\x1F\\x7F":"\\x00-\\x1F\\x7F")},whitelist:function(e,r){return t(e),e.replace(new RegExp("[^"+r+"]+","g"),"")},blacklist:m,isWhitelisted:function(e,r){t(e);for(var o=e.length-1;o>=0;o--)if(-1===r.indexOf(e[o]))return!1;return!0},normalizeEmail:function(t,e){if(e=i(e,dt),!a(t))return!1;var r=t.split("@"),o=r.pop(),n=[r.join("@"),o];if(n[1]=n[1].toLowerCase(),"gmail.com"===n[1]||"googlemail.com"===n[1]){if(e.gmail_remove_subaddress&&(n[0]=n[0].split("+")[0]),e.gmail_remove_dots&&(n[0]=n[0].replace(/\./g,"")),!n[0].length)return!1;(e.all_lowercase||e.gmail_lowercase)&&(n[0]=n[0].toLowerCase()),n[1]=e.gmail_convert_googlemaildotcom?"gmail.com":n[1]}else if(~ct.indexOf(n[1])){if(e.icloud_remove_subaddress&&(n[0]=n[0].split("+")[0]),!n[0].length)return!1;(e.all_lowercase||e.icloud_lowercase)&&(n[0]=n[0].toLowerCase())}else if(~ft.indexOf(n[1])){if(e.outlookdotcom_remove_subaddress&&(n[0]=n[0].split("+")[0]),!n[0].length)return!1;(e.all_lowercase||e.outlookdotcom_lowercase)&&(n[0]=n[0].toLowerCase())}else if(~pt.indexOf(n[1])){if(e.yahoo_remove_subaddress){var l=n[0].split("-");n[0]=l.length>1?l.slice(0,-1).join("-"):l[0]}if(!n[0].length)return!1;(e.all_lowercase||e.yahoo_lowercase)&&(n[0]=n[0].toLowerCase())}else e.all_lowercase&&(n[0]=n[0].toLowerCase());return n.join("@")},toString:o}});