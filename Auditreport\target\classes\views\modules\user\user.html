<!DOCTYPE html>
<html>
<head>
<title>用户</title>
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>
<script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak>
	<div v-show="showList">
		<div class="grid-btn">
			<a v-if="hasPermission('user:user:save')" class="btn btn-primary" @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a>
			<a v-if="hasPermission('user:user:update')" class="btn btn-primary" @click="update"><i class="fa fa-pencil-square-o"></i>&nbsp;修改</a>
			<a v-if="hasPermission('user:user:delete')" class="btn btn-primary" @click="del"><i class="fa fa-trash-o"></i>&nbsp;删除</a>
		</div>
	    <table id="jqGrid"></table>
	    <div id="jqGridPager"></div>
    </div>
    
    <div v-show="!showList" class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
		<form class="form-horizontal">
											<div class="form-group">
			   	<div class="col-sm-2 control-label">用户名</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="user.username" placeholder="用户名"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">手机号</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="user.mobile" placeholder="手机号"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">密码</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="user.password" placeholder="密码"/>
			    </div>
			</div>
									<div class="form-group">
			   	<div class="col-sm-2 control-label">创建时间</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="user.createTime" placeholder="创建时间"/>
			    </div>
			</div>
							<div class="form-group">
				<div class="col-sm-2 control-label"></div> 
				<input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
				&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
			</div>
		</form>
	</div>
</div>

<script src="../../js/modules/user/user.js"></script>
</body>
</html>